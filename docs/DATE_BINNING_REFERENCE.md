# Date and Numeric Binning Reference

This document shows how to use the date binning and numeric binning functionality that corresponds to the UI options shown in the image.

## UI Option → Code Mapping

| UI Option | Code Value | SQL Generated | Description |
|-----------|------------|---------------|-------------|
| Day | `"day"` | `DATE("Date")` | Groups by calendar day |
| Week | `"week"` | `DATE_TRUNC('week', "Date")` | Groups by week (Monday start) |
| Month | `"month"` | `DATE_TRUNC('month', "Date")` | Groups by calendar month |
| Quarter | `"quarter"` | `DATE_TRUNC('quarter', "Date")` | Groups by calendar quarter |
| Year | `"year"` | `DATE_TRUNC('year', "Date")` | Groups by calendar year |
| Day of week | `"day_of_week"` | `EXTRACT(DOW FROM "Date")` | 0=Sunday, 6=Saturday |
| Day of month | `"day_of_month"` | `EXTRACT(DAY FROM "Date")` | 1-31 |
| Day of year | `"day_of_year"` | `EXTRACT(DOY FROM "Date")` | 1-366 |
| Week of year | `"week_of_year"` | `EXTRACT(WEEK FROM "Date")` | 1-53 |
| Month of year | `"month_of_year"` | `EXTRACT(MONTH FROM "Date")` | 1-12 |
| Quarter of year | `"quarter_of_year"` | `EXTRACT(QUARTER FROM "Date")` | 1-4 |
| Year of era | `"year_of_era"` | `EXTRACT(YEAR FROM "Date")` | Full year number |
| Don't bin | `"dont_bin"` | `"Date"` | No binning, raw date |

## Numeric Binning Options

| UI Option | Code Value | SQL Generated | Description |
|-----------|------------|---------------|-------------|
| 10 bins | `{ binCount: 10 }` | `WIDTH_BUCKET(column, min, max, 10)` | Divides range into 10 equal bins |
| 20 bins | `{ binCount: 20 }` | `WIDTH_BUCKET(column, min, max, 20)` | Divides range into 20 equal bins |
| 50 bins | `{ binCount: 50 }` | `WIDTH_BUCKET(column, min, max, 50)` | Divides range into 50 equal bins |
| 100 bins | `{ binCount: 100 }` | `WIDTH_BUCKET(column, min, max, 100)` | Divides range into 100 equal bins |
| Don't bin | (no binCount) | `"Column"` | No binning, raw values |

## Usage Examples

### Date Binning Usage
```typescript
{
  "dimensions": [
    { "field": "Date", "dateBin": "month" }
  ]
}
```

### Numeric Binning Usage
```typescript
{
  "dimensions": [
    { "field": "Scope", "binCount": 10 }
  ]
}
```

### With Custom Alias
```typescript
{
  "dimensions": [
    { "field": "Date", "dateBin": "quarter", "alias": "reporting_quarter" }
  ]
}
```

### Mixed with Regular Dimensions
```typescript
{
  "dimensions": [
    { "field": "Date", "dateBin": "month" },
    "Sublayer",  // Regular string dimension
    "Activity"
  ]
}
```

### Multiple Date Binnings
```typescript
{
  "dimensions": [
    { "field": "Date", "dateBin": "year", "alias": "year" },
    { "field": "Date", "dateBin": "month_of_year", "alias": "month" }
  ]
}
```

### Mixed Date and Numeric Binning
```typescript
{
  "dimensions": [
    { "field": "Date", "dateBin": "month" },
    { "field": "Scope", "binCount": 10 },
    "Sublayer"  // Regular dimension
  ]
}
```

### Numeric Binning with Custom Alias
```typescript
{
  "dimensions": [
    { "field": "Scope", "binCount": 50, "alias": "scope_buckets" }
  ]
}
```

## Generated Aliases

When you don't specify a custom alias, the system generates one automatically:

- `{ "field": "Date", "dateBin": "month" }` → alias: `"Date_month"`
- `{ "field": "created_at", "dateBin": "week" }` → alias: `"created_at_week"`
- `{ "field": "Date", "dateBin": "day_of_week" }` → alias: `"Date_day_of_week"`

## Backward Compatibility

The old string format still works:
```typescript
{
  "dimensions": ["Date", "Sublayer"]  // Still supported
}
```

You can mix old and new formats:
```typescript
{
  "dimensions": [
    "Sublayer",  // Old format
    { "field": "Date", "dateBin": "month" }  // New format
  ]
}
```

## Notes

- Date binning only works with date/timestamp columns
- The generated SQL uses PostgreSQL date functions
- When using ORDER BY, reference the generated alias (e.g., `"Date_month"`)
- Window functions should use the original column name for ordering when possible
