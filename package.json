{"name": "query_builder", "version": "1.0.0", "description": "A powerful, type-safe SQL query builder with advanced date binning and window function support", "main": "dist/src/queryBuilder.js", "types": "dist/src/queryBuilder.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "test": "npm run test:all", "test:all": "npm run build && npm run test:sql-generation && npm run test:live-data", "test:sql-generation": "echo '=== SQL Generation Tests ===' && node dist/tests/test_date_binning.js && node dist/tests/test_numeric_binning.js && node dist/tests/test_all_window_functions.js && node dist/tests/test_metabase_expressions.js && node dist/tests/test_expression_filters.js", "test:live-data": "echo '=== Live Database Tests ===' && node dist/tests/test_date_binning_live.js && node dist/tests/test_numeric_binning_live.js && node dist/tests/test_complete_date_binning.js", "test:date-binning": "npm run build && node dist/tests/test_date_binning.js", "test:date-binning-complete": "npm run build && node dist/tests/test_complete_date_binning.js", "test:numeric-binning": "npm run build && node dist/tests/test_numeric_binning.js", "test:numeric-binning-live": "npm run build && node dist/tests/test_numeric_binning_live.js", "test:live": "npm run build && node dist/tests/test_date_binning_live.js", "test:window-functions": "npm run build && node dist/tests/test_all_window_functions.js", "test:metabase-expressions": "npm run build && node dist/tests/test_metabase_expressions.js", "test:expression-filters": "npm run build && node dist/tests/test_expression_filters.js", "example": "npm run build && node dist/examples/main.js", "example:date-binning": "npm run build && node dist/examples/date_binning_examples.js"}, "keywords": ["sql", "query-builder", "postgresql", "typescript", "date-binning", "window-functions"], "author": "", "license": "MIT", "dependencies": {"knex": "^3.1.0", "pg": "^8.16.3", "pgsql-ast-parser": "^12.0.1", "process": "^0.11.10"}, "devDependencies": {"@types/node": "^24.0.15", "typescript": "^5.8.3"}}