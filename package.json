{"name": "query_builder", "version": "1.0.0", "description": "A powerful, type-safe SQL query builder with advanced date binning and window function support", "main": "dist/src/queryBuilder.js", "types": "dist/src/queryBuilder.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "test": "npm run build && node dist/tests/test_all_window_functions.js", "test:date-binning": "npm run build && node dist/tests/test_date_binning.js", "test:date-binning-complete": "npm run build && node dist/tests/test_complete_date_binning.js", "test:live": "npm run build && node dist/tests/test_date_binning_live.js", "example": "npm run build && node dist/examples/main.js", "example:date-binning": "npm run build && node dist/examples/date_binning_examples.js"}, "keywords": ["sql", "query-builder", "postgresql", "typescript", "date-binning", "window-functions"], "author": "", "license": "MIT", "dependencies": {"knex": "^3.1.0", "pg": "^8.16.3", "pgsql-ast-parser": "^12.0.1", "process": "^0.11.10"}, "devDependencies": {"@types/node": "^24.0.15", "typescript": "^5.8.3"}}