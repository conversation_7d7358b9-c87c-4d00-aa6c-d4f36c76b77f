{"name": "query_builder", "version": "1.0.0", "description": "A powerful, type-safe SQL query builder with advanced date binning and window function support", "main": "dist/src/queryBuilder.js", "types": "dist/src/queryBuilder.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "npm run test:comprehensive", "test:comprehensive": "npm run build && echo '=== Comprehensive Window Functions & Joins Test ===' && npx tsc tests/test_window_functions_improved.ts --outDir dist_tests --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_tests/tests/test_window_functions_improved.js", "test:window-functions": "npm run build && echo '=== Window Functions Test ===' && npx tsc tests/test_window_functions_improved.ts --outDir dist_tests --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_tests/tests/test_window_functions_improved.js", "test:date-binning": "npm run build && echo '=== Date Binning Test ===' && npx tsc tests/test_complete_date_binning.ts --outDir dist_tests --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_tests/tests/test_complete_date_binning.js", "test:unified": "npm run build && echo '=== Unified Structure Test ===' && npx tsc tests/test_unified_structure.ts --outDir dist_tests --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_tests/tests/test_unified_structure.js", "test:all-window": "npm run build && echo '=== All Window Functions Test ===' && npx tsc tests/test_all_window_functions.ts --outDir dist_tests --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_tests/tests/test_all_window_functions.js", "example": "npm run example:comprehensive", "example:comprehensive": "npm run build && echo '=== Comprehensive Example ===' && npx tsc examples/comprehensive_test.ts --outDir dist_examples --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_examples/examples/comprehensive_test.js", "example:main": "npm run build && echo '=== Main Example ===' && npx tsc examples/main.ts --outDir dist_examples --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_examples/examples/main.js", "example:date-binning": "npm run build && echo '=== Date Binning Examples ===' && npx tsc examples/date_binning_examples.ts --outDir dist_examples --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_examples/examples/date_binning_examples.js", "example:cumsum": "npm run build && echo '=== Cumsum Examples ===' && npx tsc examples/cumsum_scope_example.ts --outDir dist_examples --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_examples/examples/cumsum_scope_example.js", "example:simple": "npm run build && echo '=== Simple Working Example ===' && npx tsc examples/simple_working_example.ts --outDir dist_examples --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --skipLibCheck true && node dist_examples/examples/simple_working_example.js", "dev": "npm run build:watch", "start": "npm run example", "demo": "npm run example:main"}, "keywords": ["sql", "query-builder", "postgresql", "typescript", "date-binning", "window-functions"], "author": "", "license": "MIT", "dependencies": {"knex": "^3.1.0", "pg": "^8.16.3", "pgsql-ast-parser": "^12.0.1", "process": "^0.11.10"}, "devDependencies": {"@types/node": "^24.0.15", "typescript": "^5.8.3"}}