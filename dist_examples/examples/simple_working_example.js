"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Simple working example to verify the new unified structure
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Simple Working Examples ===\n');
(async () => {
    try {
        // Example 1: Basic aggregation
        console.log('1. Basic Aggregation:');
        const cfg1 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { field: "Work done", agg: "sum", alias: "total_work" },
                { field: "Scope", agg: "avg", alias: "avg_scope" }
            ],
            filters: [
                { field: "Subactivity", op: "eq", value: "Piles Installed" }
            ],
            orderBy: [
                { column: "total_work", direction: "desc" }
            ],
            limit: 5
        };
        const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;
        console.table(rows1);
        // Example 2: Date binning
        console.log('\n2. Date Binning:');
        const cfg2 = {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" },
                { field: "Work done", agg: "sum", alias: "monthly_work" }
            ],
            filters: [
                { field: "Subactivity", op: "eq", value: "Piles Installed" }
            ],
            orderBy: [
                { column: "date_month", direction: "asc" }
            ],
            limit: 5
        };
        const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;
        console.table(rows2);
        // Example 3: Expression columns
        console.log('\n3. Expression Columns:');
        const cfg3 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { expr: "round(avg([Work done]), 2)", alias: "avg_work_rounded" },
                { expr: "count(*)", alias: "record_count" }
            ],
            filters: [
                { field: "Subactivity", op: "eq", value: "Piles Installed" }
            ],
            limit: 5
        };
        const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
        console.log('SQL:', q3.toSQL().sql);
        const rows3 = await q3;
        console.table(rows3);
        console.log('\n✅ All basic examples working perfectly!');
    }
    catch (error) {
        console.error('❌ Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
