"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Comprehensive examples of date binning functionality - Updated for New Unified Structure
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Date Binning Examples ===\n');
// All supported date binning options
const examples = [
    {
        name: "Day Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "day" },
                { field: "Work done", agg: "sum", alias: "daily_work" }
            ],
            limit: 3
        }
    },
    {
        name: "Week Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "week" },
                { field: "Work done", agg: "sum", alias: "weekly_work" }
            ],
            limit: 3
        }
    },
    {
        name: "Month Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" },
                { field: "Work done", agg: "sum", alias: "monthly_work" }
            ],
            limit: 3
        }
    },
    {
        name: "Quarter Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "quarter" },
                { field: "Work done", agg: "sum", alias: "quarterly_work" }
            ],
            limit: 3
        }
    },
    {
        name: "Year Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "year" },
                { field: "Work done", agg: "sum", alias: "yearly_work" }
            ],
            limit: 3
        }
    },
    {
        name: "Day of Week (0=Sunday, 6=Saturday)",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "day_of_week" },
                { field: "Work done", agg: "sum", alias: "work_by_weekday" }
            ],
            limit: 7
        }
    },
    {
        name: "Month of Year (1-12)",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month_of_year" },
                { field: "Work done", agg: "sum", alias: "work_by_month" }
            ],
            limit: 12
        }
    },
    {
        name: "Quarter of Year (1-4)",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "quarter_of_year" },
                { field: "Work done", agg: "sum", alias: "work_by_quarter" }
            ],
            limit: 4
        }
    },
    {
        name: "Custom Alias Example",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "quarter", alias: "reporting_quarter" },
                { field: "Sublayer" },
                { field: "Scope", agg: "sum", alias: "total_scope" }
            ],
            orderBy: [{ column: "reporting_quarter", direction: "asc" }],
            limit: 5
        }
    },
    {
        name: "Numeric Binning - 10 bins",
        config: {
            table: "progress_history",
            columns: [
                { field: "Scope", binCount: 10 },
                { field: "Date", agg: "count", alias: "record_count" }
            ],
            limit: 5
        }
    },
    {
        name: "Numeric Binning - 50 bins with custom alias",
        config: {
            table: "progress_history",
            columns: [
                { field: "Scope", binCount: 50, alias: "scope_buckets" },
                { field: "Scope", agg: "avg", alias: "avg_scope" }
            ],
            limit: 5
        }
    },
    {
        name: "Mixed Date and Numeric Binning",
        config: {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" },
                { field: "Scope", binCount: 5 },
                { field: "Work done", agg: "sum", alias: "total_work" }
            ],
            limit: 5
        }
    }
];
// Generate SQL for each example
examples.forEach((example, index) => {
    console.log(`${index + 1}. ${example.name}:`);
    const q = (0, queryBuilder_1.buildQuery)(example.config);
    console.log(`   SQL: ${q.toSQL().sql}`);
    console.log('');
});
console.log('=== Usage Notes ===');
console.log('• New unified structure uses "columns" array instead of separate dimensions/measures');
console.log('• Raw fields: { field: "Date" } for regular columns');
console.log('• Date binning: { field: "Date", dateBin: "month" } for date aggregation');
console.log('• Generated aliases follow the pattern: fieldName_dateBin (e.g., "date_month")');
console.log('• You can override the alias: { field: "Date", dateBin: "month", alias: "my_month" }');
console.log('• Aggregations: { field: "Work done", agg: "sum" } for measures');
console.log('• Window functions: { field: "Work done", agg: "cumsum", partitionBy: ["Sublayer"] }');
console.log('');
console.log('=== Numeric Binning Notes ===');
console.log('• Use binCount for numeric columns: { field: "Scope", binCount: 10 }');
console.log('• Generated aliases follow the pattern: fieldName_bin_count (e.g., "scope_bin_10")');
console.log('• Custom aliases work: { field: "Scope", binCount: 50, alias: "my_buckets" }');
console.log('• Uses PostgreSQL WIDTH_BUCKET function for automatic range calculation');
console.log('• Bin numbers start from 1 (not 0)');
console.log('• Mix with date binning: [{ field: "Date", dateBin: "month" }, { field: "Scope", binCount: 10 }]');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
