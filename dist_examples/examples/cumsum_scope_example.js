"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Example showing cumsum of Scope with different scenarios - Updated for New Unified Structure
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Cumsum of Scope Examples ===\n');
// Example 1: Simple cumsum without date binning
console.log('1. Simple cumsum of Scope by Sublayer:');
let cfg1 = {
    "table": "progress_history",
    "columns": [
        { "field": "Sublayer" },
        {
            "field": "Scope",
            "agg": "sum",
            "alias": "total_scope"
        },
        {
            "field": "Scope",
            "agg": "cumsum",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "sublayer", "direction": "asc" }
    ],
    "limit": 10
};
// Example 2: Cumsum with raw date dimension (no binning)
console.log('\n2. Cumsum of Scope with Date dimension (no binning):');
let cfg2 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date" },
        { "field": "Sublayer" },
        {
            "field": "Scope",
            "agg": "cumsum",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "date", "direction": "asc" },
        { "column": "sublayer", "direction": "asc" }
    ],
    "limit": 10
};
// Example 3: Cumsum across all data (no partition)
console.log('\n3. Global cumsum of Scope (no partition):');
let cfg3 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date" },
        { "field": "Sublayer" },
        {
            "field": "Scope",
            "agg": "sum",
            "alias": "scope"
        },
        {
            "field": "Scope",
            "agg": "cumsum",
            "orderBy": "Date",
            "alias": "global_running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "date", "direction": "asc" }
    ],
    "limit": 10
};
(async () => {
    try {
        console.log('\n--- Example 1 Results ---');
        const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;
        console.table(rows1);
        console.log('\n--- Example 2 Results ---');
        const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;
        console.table(rows2);
        console.log('\n--- Example 3 Results ---');
        const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
        console.log('SQL:', q3.toSQL().sql);
        const rows3 = await q3;
        console.table(rows3);
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
