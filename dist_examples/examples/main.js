"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// main.ts - Updated for New Unified Query Builder Structure
const queryBuilder_1 = require("../src/queryBuilder");
// Example showing the new unified structure with date binning and window functions
let cfg = {
    "table": "progress_history",
    "columns": [
        // Date binning with auto-generated alias (date_month)
        { "field": "Date", "dateBin": "month", "alias": "month" },
        // Regular field
        { "field": "Sublayer" },
        // Aggregation
        {
            "field": "Work done",
            "agg": "sum",
            "alias": "monthly_work_done"
        },
        // Window function with partitioning
        {
            "field": "Scope",
            "agg": "cumsum",
            "partitionBy": ["Sublayer", "month"], // Partition by both Sublayer and month
            "orderBy": "month", // Order by month within each partition
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "month", "direction": "asc" }, // Use column instead of field
        { "column": "sublayer", "direction": "asc" } // Auto-generated alias
    ],
    "limit": 20
};
(async () => {
    try {
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative()); // see generated SQL & bindings
        console.log('\nSQL formatted:');
        console.log(q.toSQL().sql);
        console.log('\nExecuting query...');
        const rows = await q; // execute & get rows
        console.table(rows);
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        // Close the database connection to allow the process to exit
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
