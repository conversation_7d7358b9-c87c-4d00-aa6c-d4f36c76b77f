"use strict";
// Regular aggregation functions
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateAggregationSQL = generateAggregationSQL;
/**
 * Generate SQL for regular aggregation functions
 */
function generateAggregationSQL(agg, expression) {
    switch (agg) {
        case 'sum':
            return `SUM(${expression})`;
        case 'avg':
            return `AVG(${expression})`;
        case 'count':
            return `COUNT(${expression})`;
        case 'max':
            return `MAX(${expression})`;
        case 'min':
            return `MIN(${expression})`;
        case 'median':
            return `PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ${expression})`;
        case 'stddev':
            return `STDDEV(${expression})`;
        case 'variance':
            return `VARIANCE(${expression})`;
        default:
            throw new Error(`Unsupported regular aggregation: ${agg}`);
    }
}
