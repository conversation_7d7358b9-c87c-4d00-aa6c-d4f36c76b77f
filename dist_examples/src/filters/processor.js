"use strict";
// Filter processing and SQL generation
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.applyFilters = applyFilters;
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
const defaults_1 = require("../utils/defaults");
const tableUtils_1 = require("../utils/tableUtils");
const parser_1 = require("../expressions/parser");
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
/**
 * Process and apply filters to a query
 */
function applyFilters(query, filters, mainTable, joins, allowedColumns, hasGroupBy) {
    if (!filters || filters.length === 0)
        return;
    const allTables = [mainTable, ...joins.map(j => j.table)];
    // Group filters by type
    const whereFilters = filters.filter(f => (f.type || 'where') === 'where');
    const havingFilters = filters.filter(f => f.type === 'having');
    const qualifyFilters = filters.filter(f => f.type === 'qualify');
    // Apply WHERE filters
    whereFilters.forEach(filter => {
        const processedFilter = (0, defaults_1.applyFilterDefaults)(filter);
        (0, defaults_1.validateFilter)(processedFilter);
        applyFilter(query, processedFilter, allTables, allowedColumns, false, hasGroupBy);
    });
    // Apply HAVING filters
    havingFilters.forEach(filter => {
        const processedFilter = (0, defaults_1.applyFilterDefaults)(filter);
        (0, defaults_1.validateFilter)(processedFilter);
        applyFilter(query, processedFilter, allTables, allowedColumns, true, hasGroupBy);
    });
    // Apply QUALIFY filters (PostgreSQL doesn't support QUALIFY, so we'll use a subquery approach)
    if (qualifyFilters.length > 0) {
        console.warn('QUALIFY filters are not yet fully supported in PostgreSQL. Consider using HAVING instead.');
        // TODO: Implement QUALIFY support using subqueries
    }
}
/**
 * Apply a single filter to the query
 */
function applyFilter(query, filter, tables, allowedColumns, isHaving, hasGroupBy) {
    const builder = isHaving ? query.having.bind(query) : query.where.bind(query);
    if (filter.expr) {
        // Handle expression-based filters
        const context = {
            allowedColumns,
            tables,
            hasGroupBy: isHaving,
            processedColumns: [] // This would be populated in a real implementation
        };
        // Parse Metabase-style expressions or use raw SQL
        const sqlExpression = filter.expr.includes('[') || (filter.expr.includes('(') && !filter.expr.startsWith('('))
            ? (0, parser_1.parseExpression)(filter.expr, context)
            : filter.expr;
        builder(knex.raw(sqlExpression));
    }
    else if (filter.field) {
        // Handle traditional field-based filters
        const qualifiedField = processFieldForFilter(filter.field, filter.table, tables);
        if (!filter.op) {
            throw new Error('Filter with field must have op');
        }
        switch (filter.op) {
            case 'eq':
                builder(qualifiedField, '=', filter.value);
                break;
            case 'neq':
                builder(qualifiedField, '!=', filter.value);
                break;
            case 'gt':
                builder(qualifiedField, '>', filter.value);
                break;
            case 'lt':
                builder(qualifiedField, '<', filter.value);
                break;
            case 'gte':
                builder(qualifiedField, '>=', filter.value);
                break;
            case 'lte':
                builder(qualifiedField, '<=', filter.value);
                break;
            case 'between':
                if (!Array.isArray(filter.value) || filter.value.length !== 2) {
                    throw new Error('Between filter requires array of 2 values');
                }
                builder(qualifiedField, 'between', filter.value);
                break;
            case 'in':
                if (!Array.isArray(filter.value)) {
                    throw new Error('In filter requires array of values');
                }
                builder(qualifiedField, 'in', filter.value);
                break;
            case 'like':
                builder(qualifiedField, 'like', filter.value);
                break;
            case 'ilike':
                builder(qualifiedField, 'ilike', filter.value);
                break;
            default:
                throw new Error(`Unsupported filter operator: ${filter.op}`);
        }
    }
    else {
        throw new Error('Filter must have either field or expr');
    }
}
/**
 * Process a field reference for filtering
 */
function processFieldForFilter(field, explicitTable, tables) {
    if (explicitTable) {
        // Use explicitly specified table
        const table = tables.find(t => t.qualifiedName === explicitTable || t.name === explicitTable);
        if (!table) {
            throw new Error(`Table '${explicitTable}' not found`);
        }
        return (0, tableUtils_1.qualifyFieldName)(field, table);
    }
    else if (field.includes('.')) {
        // Field is already qualified
        const table = (0, tableUtils_1.findTableForField)(field, tables);
        if (!table) {
            const tableName = field.split('.')[0];
            throw new Error(`Table '${tableName}' not found`);
        }
        return field;
    }
    else {
        // Unqualified field - return as-is (could be enhanced to auto-detect table)
        return field;
    }
}
