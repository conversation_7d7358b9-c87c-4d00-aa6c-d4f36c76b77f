// Comprehensive examples of date binning functionality
import { buildQuery, ChartConfig } from './queryBuilder';

console.log('=== Date Binning Examples ===\n');

// All supported date binning options
const examples = [
    {
        name: "Day Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "daily_work" }],
            limit: 3
        }
    },
    {
        name: "Week Binning", 
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "week" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "weekly_work" }],
            limit: 3
        }
    },
    {
        name: "Month Binning",
        config: {
            table: "progress_history", 
            dimensions: [{ field: "Date", dateBin: "month" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "monthly_work" }],
            limit: 3
        }
    },
    {
        name: "Quarter Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "quarter" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "quarterly_work" }],
            limit: 3
        }
    },
    {
        name: "Year Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "year" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "yearly_work" }],
            limit: 3
        }
    },
    {
        name: "Day of Week (0=Sunday, 6=Saturday)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_week" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "work_by_weekday" }],
            limit: 7
        }
    },
    {
        name: "Day of Month (1-31)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_month" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "work_by_day" }],
            limit: 5
        }
    },
    {
        name: "Month of Year (1-12)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "month_of_year" as const }],
            measures: [{ agg: "sum" as const, field: "Work done", alias: "work_by_month" }],
            limit: 12
        }
    },
    {
        name: "Custom Alias Example",
        config: {
            table: "progress_history",
            dimensions: [
                { field: "Date", dateBin: "quarter" as const, alias: "reporting_quarter" },
                "Sublayer"
            ],
            measures: [{ agg: "sum" as const, field: "Scope", alias: "total_scope" }],
            orderBy: [{ field: "reporting_quarter", direction: "asc" as const }],
            limit: 5
        }
    }
];

// Generate SQL for each example
examples.forEach((example, index) => {
    console.log(`${index + 1}. ${example.name}:`);
    const q = buildQuery(example.config as ChartConfig);
    console.log(`   SQL: ${q.toSQL().sql}`);
    console.log('');
});

console.log('=== Usage Notes ===');
console.log('• Use the old string format: "Date" for raw date columns');
console.log('• Use the new object format: { field: "Date", dateBin: "month" } for date binning');
console.log('• Generated aliases follow the pattern: fieldName_dateBin (e.g., "Date_month")');
console.log('• You can override the alias: { field: "Date", dateBin: "month", alias: "my_month" }');
console.log('• Mix old and new formats: ["Date", { field: "Date", dateBin: "month" }, "Sublayer"]');
console.log('• Use "dont_bin" to explicitly disable binning while using object syntax');
