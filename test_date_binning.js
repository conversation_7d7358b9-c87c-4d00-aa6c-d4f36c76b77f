"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test date binning functionality
var queryBuilder_1 = require("./queryBuilder");
console.log('=== Testing Date Binning ===\n');
// Test 1: Basic date binning options
console.log('1. Basic Date Binning Options:');
var cfg1 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" },
        "Sublayer" // Mix of old and new format
    ],
    "measures": [
        { "agg": "sum", "field": "Work done", "alias": "total_work" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 5
};
var q1 = (0, queryBuilder_1.buildQuery)(cfg1);
console.log('Month binning SQL:', q1.toSQL().sql);
console.log('Expected: DATE_TRUNC(\'month\', "Date") AS "Date_month"\n');
// Test 2: Different date binning options
console.log('2. Various Date Binning Options:');
var dateBinOptions = ['day', 'week', 'month', 'quarter', 'year', 'day_of_week', 'month_of_year'];
dateBinOptions.forEach(function (binOption) {
    var cfg = {
        "table": "progress_history",
        "dimensions": [
            { "field": "Date", "dateBin": binOption }
        ],
        "measures": [
            { "agg": "count", "field": "Work done", "alias": "count_records" }
        ],
        "limit": 3
    };
    var q = (0, queryBuilder_1.buildQuery)(cfg);
    console.log("".concat(binOption, ":"), q.toSQL().sql);
});
console.log('\n3. Custom alias with date binning:');
var cfg3 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "quarter", "alias": "reporting_quarter" }
    ],
    "measures": [
        { "agg": "sum", "field": "Scope", "alias": "total_scope" }
    ],
    "limit": 5
};
var q3 = (0, queryBuilder_1.buildQuery)(cfg3);
console.log('Custom alias SQL:', q3.toSQL().sql);
console.log('Expected: Uses "reporting_quarter" as alias\n');
console.log('=== Date Binning Test Complete ===');
