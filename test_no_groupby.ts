// Test window functions without GROUP BY (no dimensions)
import { buildQuery, ChartConfig } from './queryBuilder';

let cfg = {
    "table": "progress_history",
    "dimensions": [], // No dimensions = no GROUP BY
    "measures": [
        {
            "fn": "cumsum",
            "field": "Work done",
            "orderBy": "Date",
            "alias": "running_total"
        },
        {
            "fn": "moving_avg",
            "field": "Work done",
            "orderBy": "Date",
            "frame": { "preceding": 2, "following": null },
            "alias": "moving_avg"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" }
    ],
    "limit": 5
};

(async () => {
    const q = buildQuery(cfg as ChartConfig);
    console.log('Generated SQL (no GROUP BY):');
    console.log(q.toSQL().toNative());
})();
