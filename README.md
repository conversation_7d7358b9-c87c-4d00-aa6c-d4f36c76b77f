# Query Builder

A powerful, type-safe SQL query builder with advanced date binning and window function support.

## Features

- ✅ **Type-safe query building** with TypeScript
- ✅ **Advanced date binning** (day, week, month, quarter, year, etc.)
- ✅ **Numeric binning** (10, 50, 100 bins for histograms)
- ✅ **Window functions** with GROUP BY support (cumsum, moving averages, etc.)
- ✅ **Metabase-compatible expressions** (80+ functions for math, strings, dates, logic)
- ✅ **Security-first** with SQL injection protection
- ✅ **PostgreSQL optimized** with proper date/time functions
- ✅ **Backward compatible** API

## Directory Structure

```
query_builder/
├── src/                    # Core source code
│   ├── queryBuilder.ts     # Main query builder implementation
│   └── queryBuilder.js     # Compiled JavaScript
├── examples/               # Usage examples
│   ├── main.ts            # Basic usage example
│   ├── date_binning_examples.ts  # Date binning examples
│   └── config.json        # Sample configuration
├── tests/                  # Test files
│   ├── test_date_binning.ts
│   ├── test_window_functions.ts
│   └── ...
├── docs/                   # Documentation
│   └── DATE_BINNING_REFERENCE.md
├── dist/                   # Compiled output (generated)
├── package.json
├── tsconfig.json
└── README.md
```

## Quick Start

### Installation

```bash
npm install
```

### Basic Usage

```typescript
import { buildQuery, ChartConfig } from './src/queryBuilder';

const config: ChartConfig = {
  table: "progress_history",
  dimensions: [
    { field: "Date", dateBin: "month" },  // Date binning
    { field: "Scope", binCount: 10 },     // Numeric binning (10 bins)
    "Sublayer"                            // Regular dimension
  ],
  customColumns: [
    { expr: "case([Work done] > 500, \"High\", \"Low\")", alias: "work_category" },
    { expr: "round(abs([Work done] - [Scope]), 2)", alias: "work_diff" }
  ],
  measures: [
    { agg: "sum", field: "Work done", alias: "total_work" }
  ],
  filters: [
    { field: "Subactivity", op: "eq", value: "Piles Installed" }
  ],
  orderBy: [
    { field: "Date_month", direction: "asc" }
  ],
  limit: 100
};

const query = buildQuery(config);
const results = await query;  // Execute query
console.table(results);
```

### Date Binning Options

```typescript
// All supported date binning options
const dateBinOptions = [
  "day",             // Calendar day
  "week",            // Calendar week
  "month",           // Calendar month
  "quarter",         // Calendar quarter
  "year",            // Calendar year
  "day_of_week",     // 0=Sunday, 6=Saturday
  "day_of_month",    // 1-31
  "day_of_year",     // 1-366
  "week_of_year",    // 1-53
  "month_of_year",   // 1-12
  "quarter_of_year", // 1-4
  "year_of_era",     // Full year number
  "dont_bin"         // No binning
];

// Usage
{
  dimensions: [
    { field: "Date", dateBin: "month" },
    { field: "Date", dateBin: "day_of_week", alias: "weekday" },
    { field: "Scope", binCount: 10 }  // Numeric binning
  ]
}
```

### Numeric Binning Options

```typescript
// Supported numeric binning options
{
  dimensions: [
    { field: "Scope", binCount: 10 },     // 10 equal-width bins
    { field: "Scope", binCount: 50 },     // 50 equal-width bins
    { field: "Scope", binCount: 100 },    // 100 equal-width bins
    { field: "Scope", binCount: 20, alias: "scope_buckets" }  // Custom alias
  ]
}
```

### Metabase-Compatible Expressions

Support for 80+ Metabase expression functions across all categories:

```typescript
{
  customColumns: [
    // Math functions
    { expr: "abs([Work done])", alias: "abs_work" },
    { expr: "round([Scope], 2)", alias: "rounded_scope" },
    { expr: "sqrt([Scope])", alias: "sqrt_scope" },

    // String functions
    { expr: "upper([Sublayer])", alias: "upper_sublayer" },
    { expr: "concat([Sublayer], \" - \", [Subactivity])", alias: "combined" },
    { expr: "length([Sublayer])", alias: "sublayer_length" },

    // Date functions
    { expr: "year([Date])", alias: "year_extracted" },
    { expr: "month([Date])", alias: "month_extracted" },
    { expr: "weekday([Date])", alias: "weekday_extracted" },

    // Logical functions
    { expr: "case([Work done] > 500, \"High\", [Work done] > 100, \"Medium\", \"Low\")", alias: "work_category" },
    { expr: "coalesce([Work done], 0)", alias: "work_with_default" },
    { expr: "between([Scope], 1000, 5000)", alias: "scope_in_range" },

    // Type casting
    { expr: "text([Scope])", alias: "scope_as_text" },
    { expr: "integer([Work done])", alias: "work_as_integer" },
    { expr: "float([Scope])", alias: "scope_as_float" },

    // Complex nested expressions
    { expr: "round(abs([Work done] - [Scope]), 2)", alias: "work_scope_diff" },
    { expr: "concat(text(year([Date])), \"-\", text(month([Date])))", alias: "year_month" }
  ]
}
```

**Supported Expression Categories:**
- **Math**: abs, ceil, exp, floor, log, power, round, sqrt
- **String**: concat, contains, length, lower, upper, trim, substring, replace
- **Date**: year, month, day, hour, minute, second, weekday, quarter
- **Logical**: case/if, coalesce, between, in, isNull, notNull
- **Type Casting**: text, integer, float, date, datetime
- **Aggregations**: Average, Count, CountIf, Sum, SumIf, Max, Min, Median

### Window Functions

```typescript
{
  measures: [
    { fn: "cumsum", field: "Work done", partitionBy: ["Sublayer"], orderBy: "Date" },
    { fn: "moving_avg", field: "Work done", orderBy: "Date", frame: { preceding: 7 } },
    { fn: "delta", field: "Work done", orderBy: "Date" },
    { fn: "pct_change", field: "Work done", orderBy: "Date" }
  ]
}
```

## Running Examples

```bash
# Compile TypeScript
npm run build

# Run basic example
node examples/main.js

# Run date binning examples  
node examples/date_binning_examples.js

# Run tests
npm test                          # Window functions test
npm run test:date-binning        # Basic date binning tests
npm run test:date-binning-complete # All date binning options with live data
npm run test:live                # Live database integration tests
```

## Environment Setup

Set your PostgreSQL connection string:

```bash
export PG_URL="postgresql://user:password@host:port/database"
```

## Documentation

- [Date Binning Reference](./docs/DATE_BINNING_REFERENCE.md) - Complete guide to date binning options
- [Examples](./examples/) - Working code examples
- [Tests](./tests/) - Comprehensive test suite

## License

MIT
