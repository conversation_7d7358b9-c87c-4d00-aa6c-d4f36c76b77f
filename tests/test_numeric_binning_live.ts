// Test numeric binning with live data
import { buildQuery, ChartConfig } from '../src/queryBuilder';

console.log('=== Testing Numeric Binning with Live Data ===\n');

(async () => {
    try {
        // Test 1: Basic numeric binning with 10 bins
        console.log('1. Scope with 10 bins:');
        let cfg1 = {
            "table": "progress_history",
            "dimensions": [
                { "field": "Scope", "binCount": 10 }
            ],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" },
                { "agg": "sum" as const, "field": "Work done", "alias": "total_work" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [
                { "field": "Scope_bin_10", "direction": "asc" as const }
            ],
            "limit": 10
        };

        const q1 = buildQuery(cfg1 as ChartConfig);
        console.log('Generated SQL:');
        console.log(q1.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows1 = await q1;
        console.table(rows1);

        // Test 2: Mixed date and numeric binning
        console.log('\n\n2. Mixed Date (month) and Numeric (Scope with 5 bins):');
        let cfg2 = {
            "table": "progress_history",
            "dimensions": [
                { "field": "Date", "dateBin": "month" as const },
                { "field": "Scope", "binCount": 5 }
            ],
            "measures": [
                { "agg": "sum" as const, "field": "Work done", "alias": "monthly_work" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [
                { "field": "Date_month", "direction": "asc" as const },
                { "field": "Scope_bin_5", "direction": "asc" as const }
            ],
            "limit": 15
        };

        const q2 = buildQuery(cfg2 as ChartConfig);
        console.log('Generated SQL:');
        console.log(q2.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows2 = await q2;
        console.table(rows2);

        // Test 3: Custom alias
        console.log('\n\n3. Custom Alias for Scope Binning:');
        let cfg3 = {
            "table": "progress_history",
            "dimensions": [
                { "field": "Scope", "binCount": 8, "alias": "scope_buckets" }
            ],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "bucket_count" },
                { "agg": "avg" as const, "field": "Scope", "alias": "avg_scope" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [
                { "field": "scope_buckets", "direction": "asc" as const }
            ],
            "limit": 10
        };

        const q3 = buildQuery(cfg3 as ChartConfig);
        console.log('Generated SQL:');
        console.log(q3.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows3 = await q3;
        console.table(rows3);

        console.log('\n=== Numeric Binning Live Test Complete ===');
        
    } catch (error) {
        console.error('Error:', error);
    }
})();
