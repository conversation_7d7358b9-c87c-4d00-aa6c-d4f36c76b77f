// Test date binning with live data
import { buildQuery, cleanup } from '../src/queryBuilder';

console.log('=== Testing Date Binning with Live Data ===\n');

// Test monthly aggregation
let cfg = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "month" as const },
        { "field": "Sublayer" },
        { "field": "Work done", "agg": "sum" as const, "alias": "monthly_work" },
        { "field": "Scope", "agg": "sum" as const, "alias": "monthly_scope" }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "date_month", "direction": "asc" as const },
        { "column": "sublayer", "direction": "asc" as const }
    ],
    "limit": 20
};

(async () => {
    try {
        const q = buildQuery(cfg);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows = await q;
        console.table(rows);
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await cleanup();
        process.exit(0);
    }
})();
