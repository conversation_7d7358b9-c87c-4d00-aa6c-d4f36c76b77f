// Comprehensive test of window functions with actual SQL execution and joins
import { buildQuery, cleanup } from '../src/queryBuilder';

console.log('🚀 === COMPREHENSIVE WINDOW FUNCTIONS & JOINS TEST === 🚀\n');

let testsPassed = 0;
let testsTotal = 0;

function assert(condition: boolean, message: string) {
    testsTotal++;
    if (condition) {
        console.log(`✅ PASS: ${message}`);
        testsPassed++;
    } else {
        console.log(`❌ FAIL: ${message}`);
    }
}

(async () => {
    try {
        // Test 1: Window functions WITH GROUP BY and WITH partition - EXECUTE SQL
        console.log('🔍 Test 1: Window Functions with GROUP BY and Partition');
        const cfg1 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date" },
                { "field": "Sublayer" },
                { "field": "Work done", "agg": "sum" as const, "alias": "total_work" },
                { "field": "Work done", "agg": "cumsum" as const, "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "cumsum_partitioned" }
            ],
            "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
            "orderBy": [{ "column": "date", "direction": "asc" as const }, { "column": "sublayer", "direction": "asc" as const }],
            "limit": 5
        };

        const q1 = buildQuery(cfg1);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;

        assert(rows1.length > 0, "Query returned results");
        assert(rows1[0].hasOwnProperty('cumsum_partitioned'), "Cumsum column exists");
        assert(rows1[0].hasOwnProperty('total_work'), "Total work column exists");
        assert(typeof rows1[0]['cumsum_partitioned'] === 'string', "Cumsum value is numeric string");
        console.log('Sample results:', rows1.slice(0, 2));
        console.log('');

        // Test 2: Window functions WITH GROUP BY but WITHOUT partition - EXECUTE SQL
        console.log('🔍 Test 2: Window Functions with GROUP BY, No Partition');
        const cfg2 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date" },
                { "field": "Work done", "agg": "sum" as const, "alias": "total_work" },
                { "field": "Work done", "agg": "cumsum" as const, "orderBy": "Date", "alias": "cumsum_global" }
            ],
            "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
            "orderBy": [{ "column": "date", "direction": "asc" as const }],
            "limit": 5
        };

        const q2 = buildQuery(cfg2);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;

        assert(rows2.length > 0, "Global cumsum query returned results");
        assert(rows2[0].hasOwnProperty('cumsum_global'), "Global cumsum column exists");
        assert(!q2.toSQL().sql.includes('PARTITION BY'), "No PARTITION BY in global cumsum");
        console.log('Sample results:', rows2.slice(0, 2));
        console.log('');

        // Test 3: Date binning with window functions - EXECUTE SQL
        console.log('🔍 Test 3: Date Binning with Window Functions');
        const cfg3 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date", "dateBin": "month" as const },
                { "field": "Sublayer" },
                { "field": "Work done", "agg": "sum" as const, "alias": "monthly_work" },
                { "field": "Scope", "agg": "cumsum" as const, "partitionBy": ["Sublayer"], "orderBy": "date_month", "alias": "running_scope" }
            ],
            "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
            "orderBy": [{ "column": "date_month", "direction": "asc" as const }],
            "limit": 5
        };

        const q3 = buildQuery(cfg3);
        console.log('SQL:', q3.toSQL().sql);
        const rows3 = await q3;

        assert(rows3.length > 0, "Date binning with window functions returned results");
        assert(rows3[0].hasOwnProperty('date_month'), "Date month column exists");
        assert(rows3[0].hasOwnProperty('running_scope'), "Running scope column exists");
        assert(q3.toSQL().sql.includes('DATE_TRUNC'), "Uses DATE_TRUNC for month binning");
        console.log('Sample results:', rows3.slice(0, 2));
        console.log('');

        // Test 4: JOIN between progress_history and progress_data - EXECUTE SQL
        console.log('🔍 Test 4: JOIN Tables with Window Functions');
        const cfg4 = {
            "table": "progress_history",
            "joins": [
                {
                    "table": "progress_data",
                    "type": "inner" as const,
                    "on": { "left": "Sublayer", "right": "Sublayer" }
                }
            ],
            "columns": [
                { "field": "progress_history.Date" },
                { "field": "progress_history.Sublayer" },
                { "field": "progress_data.Scope", "alias": "planned_scope" },
                { "field": "progress_history.Work done", "agg": "sum" as const, "alias": "actual_work" },
                { "field": "progress_data.Scope", "agg": "cumsum" as const, "partitionBy": ["progress_history.Sublayer"], "orderBy": "progress_history.Date", "alias": "cumulative_planned_scope" }
            ],
            "filters": [
                { "field": "progress_history.Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [{ "column": "date", "direction": "asc" as const }],
            "limit": 5
        };

        const q4 = buildQuery(cfg4);
        console.log('SQL:', q4.toSQL().sql);
        const rows4 = await q4;

        assert(rows4.length > 0, "JOIN query returned results");
        assert(rows4[0].hasOwnProperty('planned_scope'), "Planned scope from progress_data exists");
        assert(rows4[0].hasOwnProperty('actual_work'), "Actual work from progress_history exists");
        assert(rows4[0].hasOwnProperty('cumulative_planned_scope'), "Cumulative planned scope exists");
        assert(q4.toSQL().sql.includes('INNER JOIN'), "Query contains INNER JOIN");
        console.log('Sample JOIN results:', rows4.slice(0, 2));
        console.log('');

        // Test 5: Complex JOIN with multiple tables and window functions - EXECUTE SQL
        console.log('🔍 Test 5: Complex Multi-Table JOIN with Window Functions');
        const cfg5 = {
            "table": { "name": "progress_history", "alias": "ph" },
            "joins": [
                {
                    "table": { "name": "progress_data", "alias": "pd" },
                    "type": "left" as const,
                    "on": { "left": "ph.Sublayer", "right": "pd.Sublayer" }
                }
            ],
            "columns": [
                { "field": "ph.Date" },
                { "field": "ph.Sublayer" },
                { "field": "ph.Work done", "agg": "sum" as const, "alias": "actual_work" },
                { "field": "pd.Scope", "agg": "sum" as const, "alias": "planned_scope" },
                { "field": "ph.Work done", "agg": "cumsum" as const, "partitionBy": ["ph.Sublayer"], "orderBy": "ph.Date", "alias": "cumulative_actual" },
                { "field": "pd.Scope", "agg": "cumsum" as const, "partitionBy": ["ph.Sublayer"], "orderBy": "ph.Date", "alias": "cumulative_planned" }
            ],
            "filters": [
                { "field": "ph.Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [{ "column": "date", "direction": "asc" as const }],
            "limit": 5
        };

        const q5 = buildQuery(cfg5);
        console.log('SQL:', q5.toSQL().sql);
        const rows5 = await q5;

        assert(rows5.length > 0, "Complex JOIN query returned results");
        assert(rows5[0].hasOwnProperty('actual_work'), "Actual work column exists");
        assert(rows5[0].hasOwnProperty('planned_scope'), "Planned scope column exists");
        assert(rows5[0].hasOwnProperty('cumulative_actual'), "Cumulative actual column exists");
        assert(rows5[0].hasOwnProperty('cumulative_planned'), "Cumulative planned column exists");
        assert(q5.toSQL().sql.includes('LEFT JOIN'), "Query contains LEFT JOIN");
        assert(q5.toSQL().sql.includes('ph'), "Query uses table aliases");
        console.log('Sample complex JOIN results:', rows5.slice(0, 2));
        console.log('');

        // Test 6: Window functions without GROUP BY (raw data) - EXECUTE SQL
        console.log('🔍 Test 6: Window Functions without GROUP BY (Raw Data)');
        const cfg6 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date" },
                { "field": "Sublayer" },
                { "field": "Work done" },
                { "field": "Work done", "agg": "cumsum" as const, "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "running_total" },
                { "field": "Work done", "agg": "row_number" as const, "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "row_num" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
            ],
            "orderBy": [{ "column": "date", "direction": "asc" as const }],
            "limit": 5
        };

        const q6 = buildQuery(cfg6);
        console.log('SQL:', q6.toSQL().sql);
        const rows6 = await q6;

        assert(rows6.length > 0, "Raw data window functions returned results");
        assert(rows6[0].hasOwnProperty('running_total'), "Running total column exists");
        assert(rows6[0].hasOwnProperty('row_num'), "Row number column exists");
        assert(!q6.toSQL().sql.includes('GROUP BY'), "No GROUP BY in raw data query");
        assert(q6.toSQL().sql.includes('SUM(SUM("Work done")) OVER'), "Uses nested aggregation (correct for GROUP BY scenario)");
        console.log('Sample raw data results:', rows6.slice(0, 2));
        console.log('');

        // Test Summary
        console.log('🎉 === TEST SUMMARY === 🎉');
        console.log(`✅ Tests Passed: ${testsPassed}/${testsTotal}`);
        console.log(`📊 Success Rate: ${Math.round((testsPassed / testsTotal) * 100)}%`);

        if (testsPassed === testsTotal) {
            console.log('🏆 ALL TESTS PASSED - WINDOW FUNCTIONS & JOINS ARE ROCK SOLID!');
        } else {
            console.log('❌ Some tests failed - check the output above');
        }

    } catch (error) {
        console.error('💥 ERROR:', error);
    } finally {
        await cleanup();
        process.exit(0);
    }
})();
