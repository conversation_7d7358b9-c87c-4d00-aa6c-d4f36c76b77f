// Test improved window functions with hasGroupBy and partition support
import { buildQuery, ChartConfig, cleanup } from '../src/queryBuilder';

console.log('=== Testing Improved Window Functions ===\n');

// Test 1: Window functions WITH GROUP BY and WITH partition
console.log('1. WITH GROUP BY + WITH PARTITION:');
let cfg1 = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],  // This creates GROUP BY
    "measures": [
        { "fn": "cumsum", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "cumsum_partitioned" },
        { "fn": "moving_avg", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "frame": { "preceding": 3 }, "alias": "moving_avg_partitioned" },
        { "fn": "rank", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Work done", "alias": "rank_partitioned" },
        { "fn": "delta", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "delta_partitioned" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 5
};

const q1 = buildQuery(cfg1 as ChartConfig);
console.log('SQL:', q1.toSQL().sql);
console.log('Expected: sum(sum("Work done")) OVER (PARTITION BY "Sublayer" ORDER BY ...), etc.\n');

// Test 2: Window functions WITH GROUP BY but WITHOUT partition
console.log('2. WITH GROUP BY + WITHOUT PARTITION:');
let cfg2 = {
    "table": "progress_history",
    "dimensions": ["Date"],  // This creates GROUP BY
    "measures": [
        { "fn": "cumsum", "field": "Work done", "orderBy": "Date", "alias": "cumsum_global" },
        { "fn": "moving_sum", "field": "Work done", "orderBy": "Date", "frame": { "preceding": 2 }, "alias": "moving_sum_global" },
        { "fn": "pct_change", "field": "Work done", "orderBy": "Date", "alias": "pct_change_global" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 5
};

const q2 = buildQuery(cfg2 as ChartConfig);
console.log('SQL:', q2.toSQL().sql);
console.log('Expected: sum(sum("Work done")) OVER (ORDER BY ...) - no PARTITION BY\n');

// Test 3: Window functions WITHOUT GROUP BY but WITH partition
console.log('3. WITHOUT GROUP BY + WITH PARTITION:');
let cfg3 = {
    "table": "progress_history",
    "dimensions": [],  // No GROUP BY
    "measures": [
        { "fn": "cumsum", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "cumsum_raw_partitioned" },
        { "fn": "moving_avg", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "frame": { "preceding": 2 }, "alias": "moving_avg_raw_partitioned" },
        { "fn": "row_number", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "row_number_partitioned" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 5
};

const q3 = buildQuery(cfg3 as ChartConfig);
console.log('SQL:', q3.toSQL().sql);
console.log('Expected: sum("Work done") OVER (PARTITION BY "Sublayer" ORDER BY ...) - no nested aggregation\n');

// Test 4: Window functions WITHOUT GROUP BY and WITHOUT partition
console.log('4. WITHOUT GROUP BY + WITHOUT PARTITION:');
let cfg4 = {
    "table": "progress_history",
    "dimensions": [],  // No GROUP BY
    "measures": [
        { "fn": "cumsum", "field": "Work done", "orderBy": "Date", "alias": "cumsum_raw_global" },
        { "fn": "dense_rank", "field": "Work done", "orderBy": "Work done", "alias": "dense_rank_global" },
        { "fn": "median", "field": "Work done", "alias": "median_global" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 5
};

const q4 = buildQuery(cfg4 as ChartConfig);
console.log('SQL:', q4.toSQL().sql);
console.log('Expected: sum("Work done") OVER (ORDER BY ...) - no PARTITION BY, no nested aggregation\n');

console.log('=== Improved Window Functions Test Complete ===');

// Clean up and exit
cleanup().then(() => process.exit(0));
