// Test all date binning options with live data
import { buildQuery, cleanup } from '../src/queryBuilder';

console.log('=== Testing All Date Binning Options with Live Data ===\n');

const allDateBinOptions = [
    { bin: 'day' as const, description: 'Calendar Day' },
    { bin: 'week' as const, description: 'Calendar Week' },
    { bin: 'month' as const, description: 'Calendar Month' },
    { bin: 'quarter' as const, description: 'Calendar Quarter' },
    { bin: 'year' as const, description: 'Calendar Year' },
    { bin: 'day_of_week' as const, description: 'Day of Week (0=Sunday, 6=Saturday)' },
    { bin: 'month_of_year' as const, description: 'Month of Year (1-12)' },
    { bin: 'quarter_of_year' as const, description: 'Quarter of Year (1-4)' }
];

(async () => {
    try {
        for (const option of allDateBinOptions) {
            console.log(`\n--- ${option.description} ---`);

            const cfg = {
                table: "progress_history",
                columns: [
                    { field: "Date", dateBin: option.bin },
                    { field: "Work done", agg: "sum" as const, alias: "total_work" },
                    { field: "Date", agg: "count" as const, alias: "record_count" }
                ],
                filters: [
                    { field: "Subactivity", op: "eq" as const, value: "Piles Installed" }
                ],
                orderBy: [
                    { column: `date_${option.bin}`, direction: "asc" as const }
                ],
                limit: 5
            };

            const q = buildQuery(cfg);
            console.log(`SQL: ${q.toSQL().sql}`);

            const rows = await q;
            console.table(rows);
        }

        console.log('\n=== All Date Binning Options Test Complete ===');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await cleanup();
        process.exit(0);
    }
})();
