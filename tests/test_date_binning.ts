// Test date binning functionality
import { buildQuery, cleanup } from '../src/queryBuilder';

console.log('=== Testing Date Binning ===\n');

// Test 1: Basic date binning options
console.log('1. Basic Date Binning Options:');
let cfg1 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "month" as const },
        { "field": "Sublayer" },
        { "field": "Work done", "agg": "sum" as const, "alias": "total_work" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 5
};

const q1 = buildQuery(cfg1);
console.log('Month binning SQL:', q1.toSQL().sql);
console.log('Expected: DATE_TRUNC(\'month\', "Date") AS "Date_month"\n');

// Test 2: Different date binning options
console.log('2. Various Date Binning Options:');
const dateBinOptions = ['day', 'week', 'month', 'quarter', 'year', 'day_of_week', 'month_of_year'] as const;

dateBinOptions.forEach(binOption => {
    let cfg = {
        "table": "progress_history",
        "columns": [
            { "field": "Date", "dateBin": binOption },
            { "field": "Work done", "agg": "count" as const, "alias": "count_records" }
        ],
        "limit": 3
    };

    const q = buildQuery(cfg);
    console.log(`${binOption}:`, q.toSQL().sql);
});

console.log('\n3. Custom alias with date binning:');
let cfg3 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "quarter" as const, "alias": "reporting_quarter" },
        { "field": "Scope", "agg": "sum" as const, "alias": "total_scope" }
    ],
    "limit": 5
};

const q3 = buildQuery(cfg3);
console.log('Custom alias SQL:', q3.toSQL().sql);
console.log('Expected: Uses "reporting_quarter" as alias\n');

console.log('=== Date Binning Test Complete ===');

// Clean up and exit
cleanup().then(() => process.exit(0));
