// Test window functions without GROUP BY (no dimensions)
import { buildQuery, cleanup } from '../src/queryBuilder';

let cfg = {
    "table": "progress_history",
    "columns": [
        {
            "field": "Work done",
            "agg": "cumsum" as const,
            "orderBy": "Date",
            "alias": "running_total"
        },
        {
            "field": "Work done",
            "agg": "moving_avg" as const,
            "orderBy": "Date",
            "frame": { "preceding": 2 },
            "alias": "moving_avg"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "Date", "direction": "asc" as const }
    ],
    "limit": 5
};

(async () => {
    try {
        const q = buildQuery(cfg);
        console.log('Generated SQL (no GROUP BY):');
        console.log(q.toSQL().toNative());

        const result = await q;
        console.table(result);
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await cleanup();
    }
})();
