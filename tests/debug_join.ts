// Debug join GROUP BY issue
import { buildQuery, cleanup } from '../src/queryBuilder';

console.log('=== Debug JOIN GROUP BY Issue ===\n');

(async () => {
    try {
        // Simple join test
        const cfg = {
            "table": "progress_history",
            "joins": [
                {
                    "table": "progress_data",
                    "type": "inner" as const,
                    "on": { "left": "Sublayer", "right": "Sublayer" }
                }
            ],
            "columns": [
                { "field": "progress_history.Date" },
                { "field": "progress_history.Sublayer" },
                { "field": "progress_history.Work done", "agg": "sum" as const, "alias": "actual_work" }
            ],
            "limit": 3
        };

        const q = buildQuery(cfg);
        console.log('Generated SQL:', q.toSQL().sql);

        // Let's see what the GROUP BY should be
        console.log('\nExpected GROUP BY should use full column references:');
        console.log('GROUP BY "progress_history"."Date", "progress_history"."Sublayer"');

        // Let's also test without the table prefix to see if that works
        console.log('\n--- Testing without table prefix ---');
        const cfg2 = {
            "table": "progress_history",
            "joins": [
                {
                    "table": "progress_data",
                    "type": "inner" as const,
                    "on": { "left": "Sublayer", "right": "Sublayer" }
                }
            ],
            "columns": [
                { "field": "Date", "table": "progress_history" },
                { "field": "Sublayer", "table": "progress_history" },
                { "field": "Work done", "table": "progress_history", "agg": "sum" as const, "alias": "actual_work" }
            ],
            "limit": 3
        };

        const q2 = buildQuery(cfg2);
        console.log('Generated SQL with explicit table:', q2.toSQL().sql);

        // Test 3: Add window function to join
        console.log('\n--- Testing JOIN with window function ---');
        const cfg3 = {
            "table": "progress_history",
            "joins": [
                {
                    "table": "progress_data",
                    "type": "inner" as const,
                    "on": { "left": "Sublayer", "right": "Sublayer" }
                }
            ],
            "columns": [
                { "field": "progress_history.Date" },
                { "field": "progress_history.Sublayer" },
                { "field": "progress_history.Work done", "agg": "sum" as const, "alias": "actual_work" },
                { "field": "progress_data.Scope", "agg": "cumsum" as const, "partitionBy": ["progress_history.Sublayer"], "orderBy": "progress_history.Date", "alias": "cumulative_scope" }
            ],
            "limit": 3
        };

        const q3 = buildQuery(cfg3);
        console.log('Generated SQL with window function:', q3.toSQL().sql);

        try {
            const rows3 = await q3;
            console.log('✅ JOIN with window function works!');
        } catch (error) {
            console.log('❌ JOIN with window function failed:', error.message);
        }

    } catch (error) {
        console.error('ERROR:', error);
    } finally {
        await cleanup();
        process.exit(0);
    }
})();
