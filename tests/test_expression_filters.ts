// Test Metabase expressions in filters
import { buildQueryAuto, cleanup } from '../src/queryBuilder';

console.log('=== Testing Metabase Expressions in Filters ===\n');

(async () => {
    try {
        // Test 1: Simple expression filter
        console.log('1. Testing Simple Expression Filter:');
        let cfg1 = {
            "table": "progress_history",
            "dimensions": ["Sublayer"],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" },
                { "agg": "sum" as const, "field": "Work done", "alias": "total_work" }
            ],
            "filters": [
                { "expr": "abs([Work done] - [Scope]) > 1000" }  // Expression filter
            ],
            "limit": 3
        };

        const q1 = buildQueryAuto(cfg1);
        console.log('Generated SQL:', q1.toSQL().sql);
        const result1 = await q1;
        console.table(result1);
        console.log(`✅ Found ${result1.length} records with work-scope difference > 1000\n`);

        // Test 2: String manipulation in filter
        console.log('2. Testing String Expression Filter:');
        let cfg2 = {
            "table": "progress_history",
            "dimensions": ["Sublayer"],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" }
            ],
            "filters": [
                { "expr": "upper([Sublayer]) = 'BLOCK-1'" }  // String manipulation filter
            ],
            "limit": 3
        };

        const q2 = buildQueryAuto(cfg2);
        console.log('Generated SQL:', q2.toSQL().sql);
        const result2 = await q2;
        console.table(result2);
        console.log(`✅ Found ${result2.length} records for Block-1\n`);

        // Test 3: Date extraction in filter
        console.log('3. Testing Date Expression Filter:');
        let cfg3 = {
            "table": "progress_history",
            "dimensions": ["Sublayer"],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" }
            ],
            "filters": [
                { "expr": "year([Date]) = 2025" }  // Date extraction filter
            ],
            "limit": 3
        };

        const q3 = buildQueryAuto(cfg3);
        console.log('Generated SQL:', q3.toSQL().sql);
        const result3 = await q3;
        console.table(result3);
        console.log(`✅ Found ${result3.length} records for year 2025\n`);

        // Test 4: Complex logical expression filter
        console.log('4. Testing Complex Logical Expression Filter:');
        let cfg4 = {
            "table": "progress_history",
            "dimensions": ["Sublayer"],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" },
                { "agg": "avg" as const, "field": "Work done", "alias": "avg_work" }
            ],
            "filters": [
                { "expr": "case([Work done] > 500, 'High', 'Low') = 'High'" }  // Complex logical filter
            ],
            "limit": 3
        };

        const q4 = buildQueryAuto(cfg4);
        console.log('Generated SQL:', q4.toSQL().sql);
        const result4 = await q4;
        console.table(result4);
        console.log(`✅ Found ${result4.length} records with high work done\n`);

        // Test 5: Mixed traditional and expression filters
        console.log('5. Testing Mixed Traditional and Expression Filters:');
        let cfg5 = {
            "table": "progress_history",
            "dimensions": ["Sublayer"],
            "measures": [
                { "agg": "count" as const, "field": "Date", "alias": "record_count" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" },  // Traditional filter
                { "expr": "month([Date]) >= 6" }  // Expression filter
            ],
            "limit": 3
        };

        const q5 = buildQueryAuto(cfg5);
        console.log('Generated SQL:', q5.toSQL().sql);
        const result5 = await q5;
        console.table(result5);
        console.log(`✅ Found ${result5.length} records with Piles Installed from June onwards\n`);

        console.log('🎉 All Expression Filter Tests Passed! 🎉');
        console.log('✅ Simple Expression Filters: Working');
        console.log('✅ String Expression Filters: Working');
        console.log('✅ Date Expression Filters: Working');
        console.log('✅ Complex Logical Expression Filters: Working');
        console.log('✅ Mixed Traditional + Expression Filters: Working');

    } catch (error) {
        console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    } finally {
        await cleanup();
        process.exit(0);
    }
})();
