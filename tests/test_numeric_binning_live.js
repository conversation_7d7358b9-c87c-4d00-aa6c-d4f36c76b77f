"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Test numeric binning with live data
var queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Numeric Binning with Live Data ===\n');
(function () { return __awaiter(void 0, void 0, void 0, function () {
    var cfg1, q1, rows1, cfg2, q2, rows2, cfg3, q3, rows3, error_1;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 4, , 5]);
                // Test 1: Basic numeric binning with 10 bins
                console.log('1. Scope with 10 bins:');
                cfg1 = {
                    "table": "progress_history",
                    "dimensions": [
                        { "field": "Scope", "binCount": 10 }
                    ],
                    "measures": [
                        { "agg": "count", "field": "Date", "alias": "record_count" },
                        { "agg": "sum", "field": "Work done", "alias": "total_work" }
                    ],
                    "filters": [
                        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
                    ],
                    "orderBy": [
                        { "field": "Scope_bin_10", "direction": "asc" }
                    ],
                    "limit": 10
                };
                q1 = (0, queryBuilder_1.buildQuery)(cfg1);
                console.log('Generated SQL:');
                console.log(q1.toSQL().toNative());
                console.log('\nExecuting query...');
                return [4 /*yield*/, q1];
            case 1:
                rows1 = _a.sent();
                console.table(rows1);
                // Test 2: Mixed date and numeric binning
                console.log('\n\n2. Mixed Date (month) and Numeric (Scope with 5 bins):');
                cfg2 = {
                    "table": "progress_history",
                    "dimensions": [
                        { "field": "Date", "dateBin": "month" },
                        { "field": "Scope", "binCount": 5 }
                    ],
                    "measures": [
                        { "agg": "sum", "field": "Work done", "alias": "monthly_work" }
                    ],
                    "filters": [
                        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
                    ],
                    "orderBy": [
                        { "field": "Date_month", "direction": "asc" },
                        { "field": "Scope_bin_5", "direction": "asc" }
                    ],
                    "limit": 15
                };
                q2 = (0, queryBuilder_1.buildQuery)(cfg2);
                console.log('Generated SQL:');
                console.log(q2.toSQL().toNative());
                console.log('\nExecuting query...');
                return [4 /*yield*/, q2];
            case 2:
                rows2 = _a.sent();
                console.table(rows2);
                // Test 3: Custom alias
                console.log('\n\n3. Custom Alias for Scope Binning:');
                cfg3 = {
                    "table": "progress_history",
                    "dimensions": [
                        { "field": "Scope", "binCount": 8, "alias": "scope_buckets" }
                    ],
                    "measures": [
                        { "agg": "count", "field": "Date", "alias": "bucket_count" },
                        { "agg": "avg", "field": "Scope", "alias": "avg_scope" }
                    ],
                    "filters": [
                        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
                    ],
                    "orderBy": [
                        { "field": "scope_buckets", "direction": "asc" }
                    ],
                    "limit": 10
                };
                q3 = (0, queryBuilder_1.buildQuery)(cfg3);
                console.log('Generated SQL:');
                console.log(q3.toSQL().toNative());
                console.log('\nExecuting query...');
                return [4 /*yield*/, q3];
            case 3:
                rows3 = _a.sent();
                console.table(rows3);
                console.log('\n=== Numeric Binning Live Test Complete ===');
                return [3 /*break*/, 5];
            case 4:
                error_1 = _a.sent();
                console.error('Error:', error_1);
                return [3 /*break*/, 5];
            case 5: return [2 /*return*/];
        }
    });
}); })();
