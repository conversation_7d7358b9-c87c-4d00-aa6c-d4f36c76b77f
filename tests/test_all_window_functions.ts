// Test all window functions in both GROUP BY and non-GROUP BY scenarios
import { buildQuery, cleanup } from '../src/queryBuilder';

async function runAllWindowFunctionsTest() {
    console.log('=== Testing Window Functions ===\n');

    // Test 1: With GROUP BY (dimensions present)
    console.log('1. WITH GROUP BY (dimensions present):');
    let cfgWithGroupBy = {
        "table": "progress_history",
        "columns": [
            { "field": "Date" },
            { "field": "Sublayer" },
            { "field": "Work done", "agg": "cumsum" as const, "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "cumsum_test" },
            { "field": "Work done", "agg": "moving_avg" as const, "orderBy": "Date", "frame": { "preceding": 2 }, "alias": "moving_avg_test" },
            { "field": "Work done", "agg": "moving_sum" as const, "orderBy": "Date", "frame": { "preceding": 1 }, "alias": "moving_sum_test" },
            { "field": "Work done", "agg": "delta" as const, "orderBy": "Date", "alias": "delta_test" },
            { "field": "Work done", "agg": "pct_change" as const, "orderBy": "Date", "alias": "pct_change_test" },
            { "field": "Work done", "agg": "rank" as const, "orderBy": "Work done", "alias": "rank_test" },
            { "field": "Work done", "agg": "dense_rank" as const, "orderBy": "Work done", "alias": "dense_rank_test" },
            { "field": "Work done", "agg": "row_number" as const, "orderBy": "Date", "alias": "row_number_test" }
        ],
        "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
        "limit": 3
    };

    const qWithGroupBy = buildQuery(cfgWithGroupBy);
    console.log('SQL:', qWithGroupBy.toSQL().sql);
    console.log('Expected: Uses sum(sum("Work done")), avg(sum("Work done")), etc.\n');

    // Execute the query
    try {
        const rowsWithGroupBy = await qWithGroupBy;
        console.log('✅ GROUP BY query executed successfully');
        console.log(`📊 Returned ${rowsWithGroupBy.length} rows`);
        if (rowsWithGroupBy.length > 0) {
            console.table(rowsWithGroupBy.slice(0, 3)); // Show first 3 rows
        }
    } catch (error) {
        console.error('❌ GROUP BY query failed:', error instanceof Error ? error.message : String(error));
    }
    console.log();

    // Test 2: Without GROUP BY (no dimensions)
    console.log('2. WITHOUT GROUP BY (no dimensions):');
    let cfgWithoutGroupBy = {
        "table": "progress_history",
        "columns": [
            { "field": "Work done", "agg": "cumsum" as const, "orderBy": "Date", "alias": "cumsum_test" },
            { "field": "Work done", "agg": "moving_avg" as const, "orderBy": "Date", "frame": { "preceding": 2 }, "alias": "moving_avg_test" },
            { "field": "Work done", "agg": "moving_sum" as const, "orderBy": "Date", "frame": { "preceding": 1 }, "alias": "moving_sum_test" },
            { "field": "Work done", "agg": "delta" as const, "orderBy": "Date", "alias": "delta_test" },
            { "field": "Work done", "agg": "pct_change" as const, "orderBy": "Date", "alias": "pct_change_test" },
            { "field": "Work done", "agg": "rank" as const, "orderBy": "Work done", "alias": "rank_test" },
            { "field": "Work done", "agg": "dense_rank" as const, "orderBy": "Work done", "alias": "dense_rank_test" },
            { "field": "Work done", "agg": "row_number" as const, "orderBy": "Date", "alias": "row_number_test" }
        ],
        "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
        "limit": 3
    };

    const qWithoutGroupBy = buildQuery(cfgWithoutGroupBy);
    console.log('SQL:', qWithoutGroupBy.toSQL().sql);
    console.log('Expected: Uses sum("Work done"), avg("Work done"), etc. (no nested aggregation)\n');

    // Execute the query
    try {
        const rowsWithoutGroupBy = await qWithoutGroupBy;
        console.log('✅ Non-GROUP BY query executed successfully');
        console.log(`📊 Returned ${rowsWithoutGroupBy.length} rows`);
        if (rowsWithoutGroupBy.length > 0) {
            console.table(rowsWithoutGroupBy.slice(0, 3)); // Show first 3 rows
        }
    } catch (error) {
        console.error('❌ Non-GROUP BY query failed:', error instanceof Error ? error.message : String(error));
    }
    console.log();

    console.log('=== Test Complete ===');

    // Clean up and exit
    await cleanup();
}

// Run the test
runAllWindowFunctionsTest().then(() => process.exit(0)).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
});
