// Test date binning with live data
import { buildQuery, ChartConfig } from './queryBuilder';

console.log('=== Testing Date Binning with Live Data ===\n');

// Test monthly aggregation
let cfg = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" as const },
        "Sublayer"
    ],
    "measures": [
        { "agg": "sum" as const, "field": "Work done", "alias": "monthly_work" },
        { "agg": "sum" as const, "field": "Scope", "alias": "monthly_scope" }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date_month", "direction": "asc" as const },
        { "field": "Sublayer", "direction": "asc" as const }
    ],
    "limit": 20
};

(async () => {
    try {
        const q = buildQuery(cfg as ChartConfig);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows = await q;
        console.table(rows);
    } catch (error) {
        console.error('Error:', error);
    }
})();
