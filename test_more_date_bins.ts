// Test more date binning options with live data
import { buildQuery, ChartConfig } from './queryBuilder';

console.log('=== Testing More Date Binning Options ===\n');

// Test 1: Day of week analysis
console.log('1. Day of Week Analysis:');
let cfg1 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "day_of_week" as const, "alias": "weekday" }
    ],
    "measures": [
        { "agg": "sum" as const, "field": "Work done", "alias": "total_work" },
        { "agg": "count" as const, "field": "Date", "alias": "record_count" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "orderBy": [{ "field": "weekday", "direction": "asc" as const }],
    "limit": 10
};

// Test 2: Quarter analysis
console.log('2. Quarterly Analysis:');
let cfg2 = {
    "table": "progress_history", 
    "dimensions": [
        { "field": "Date", "dateBin": "quarter" as const }
    ],
    "measures": [
        { "agg": "sum" as const, "field": "Scope", "alias": "quarterly_scope" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }],
    "limit": 10
};

(async () => {
    try {
        console.log('\n--- Day of Week Results ---');
        const q1 = buildQuery(cfg1 as ChartConfig);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;
        console.table(rows1);
        
        console.log('\n--- Quarterly Results ---');
        const q2 = buildQuery(cfg2 as ChartConfig);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;
        console.table(rows2);
        
    } catch (error) {
        console.error('Error:', error);
    }
})();
