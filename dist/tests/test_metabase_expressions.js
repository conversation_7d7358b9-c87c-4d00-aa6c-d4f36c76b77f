"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test Metabase-compatible expressions
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Metabase-Compatible Expressions ===\n');
// Test 1: Math Functions
console.log('1. Math Functions:');
let cfg1 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "abs([Work done])", "alias": "abs_work" },
        { "expr": "round([Work done], 2)", "alias": "rounded_work" },
        { "expr": "ceil([Scope])", "alias": "ceiling_scope" },
        { "expr": "floor([Scope])", "alias": "floor_scope" },
        { "expr": "sqrt([Scope])", "alias": "sqrt_scope" }
    ],
    "dimensions": ["Sublayer"],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 3
};
const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
console.log('SQL:', q1.toSQL().sql);
// Test 2: String Functions
console.log('\n2. String Functions:');
let cfg2 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "upper([Sublayer])", "alias": "upper_sublayer" },
        { "expr": "lower([Sublayer])", "alias": "lower_sublayer" },
        { "expr": "length([Sublayer])", "alias": "sublayer_length" },
        { "expr": "concat([Sublayer], \" - \", [Subactivity])", "alias": "combined_info" }
    ],
    "dimensions": ["Date"],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "limit": 3
};
const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
console.log('SQL:', q2.toSQL().sql);
// Test 3: Date Functions
console.log('\n3. Date Functions:');
let cfg3 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "year([Date])", "alias": "year_extracted" },
        { "expr": "month([Date])", "alias": "month_extracted" },
        { "expr": "day([Date])", "alias": "day_extracted" },
        { "expr": "weekday([Date])", "alias": "weekday_extracted" }
    ],
    "dimensions": ["Sublayer"],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "limit": 3
};
const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
console.log('SQL:', q3.toSQL().sql);
// Test 4: Logical Functions
console.log('\n4. Logical Functions:');
let cfg4 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "case([Work done] > 500, \"High\", [Work done] > 100, \"Medium\", \"Low\")", "alias": "work_category" },
        { "expr": "coalesce([Work done], 0)", "alias": "work_with_default" },
        { "expr": "isNull([Work done])", "alias": "is_work_null" },
        { "expr": "between([Scope], 1000, 5000)", "alias": "scope_in_range" }
    ],
    "dimensions": ["Sublayer"],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "limit": 3
};
const q4 = (0, queryBuilder_1.buildQuery)(cfg4);
console.log('SQL:', q4.toSQL().sql);
// Test 5: Type Casting Functions
console.log('\n5. Type Casting Functions:');
let cfg5 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "text([Scope])", "alias": "scope_as_text" },
        { "expr": "integer([Work done])", "alias": "work_as_integer" },
        { "expr": "float([Scope])", "alias": "scope_as_float" }
    ],
    "dimensions": ["Sublayer"],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "limit": 3
};
const q5 = (0, queryBuilder_1.buildQuery)(cfg5);
console.log('SQL:', q5.toSQL().sql);
// Test 6: Complex Nested Expressions
console.log('\n6. Complex Nested Expressions:');
let cfg6 = {
    "table": "progress_history",
    "customColumns": [
        { "expr": "round(abs([Work done] - [Scope]), 2)", "alias": "work_scope_diff" },
        { "expr": "case(upper([Sublayer]) = \"BLOCK-1\", \"Primary\", \"Secondary\")", "alias": "block_priority" },
        { "expr": "concat(text(year([Date])), \"-\", text(month([Date])))", "alias": "year_month" }
    ],
    "dimensions": ["Sublayer"],
    "measures": [
        { "agg": "avg", "field": "Work done", "alias": "avg_work" }
    ],
    "limit": 3
};
const q6 = (0, queryBuilder_1.buildQuery)(cfg6);
console.log('SQL:', q6.toSQL().sql);
console.log('\n=== Metabase Expressions Test Complete ===');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=test_metabase_expressions.js.map