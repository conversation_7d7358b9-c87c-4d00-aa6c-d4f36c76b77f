{"version": 3, "file": "test_window_functions_improved.js", "sourceRoot": "", "sources": ["../../tests/test_window_functions_improved.ts"], "names": [], "mappings": ";;AAAA,uEAAuE;AACvE,sDAAuE;AAEvE,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;AAE3D,4DAA4D;AAC5D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAClD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAG,wBAAwB;IAC7D,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE;QACvH,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;QAC5J,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE;QACxH,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;KACxH;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;AAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,qFAAqF,CAAC,CAAC;AAEnG,+DAA+D;AAC/D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACrD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,CAAC,MAAM,CAAC,EAAG,wBAAwB;IACjD,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;QACrF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE;QAC1H,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;KAChG;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;AAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC,CAAC;AAEvF,+DAA+D;AAC/D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACrD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,EAAE,EAAG,cAAc;IACjC,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE;QAC3H,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;QAChK,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE;KAClI;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;AAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,kGAAkG,CAAC,CAAC;AAEhH,kEAAkE;AAClE,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;AACxD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,EAAE,EAAG,cAAc;IACjC,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;QACzF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,mBAAmB,EAAE;QAClG,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE;KACrE;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;AAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,2FAA2F,CAAC,CAAC;AAEzG,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;AAE/D,oBAAoB;AACpB,IAAA,sBAAO,GAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC"}