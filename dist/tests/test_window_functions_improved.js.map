{"version": 3, "file": "test_window_functions_improved.js", "sourceRoot": "", "sources": ["../../tests/test_window_functions_improved.ts"], "names": [], "mappings": ";;AAAA,6EAA6E;AAC7E,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;AAE3E,IAAI,WAAW,GAAG,CAAC,CAAC;AACpB,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB,SAAS,MAAM,CAAC,SAAkB,EAAE,OAAe;IAC/C,UAAU,EAAE,CAAC;IACb,IAAI,SAAS,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;QAClC,WAAW,EAAE,CAAC;IAClB,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;IACtC,CAAC;AACL,CAAC;AAED,CAAC,KAAK,IAAI,EAAE;IACR,IAAI,CAAC;QACD,0EAA0E;QAC1E,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACvE,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,OAAO,EAAE,UAAU,EAAE;gBACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,YAAY,EAAE;gBACtE,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE;aACpI;YACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACxF,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YACrH,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,wBAAwB,CAAC,CAAC;QACnD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,sBAAsB,CAAC,CAAC;QAC9E,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,0BAA0B,CAAC,CAAC;QAC1E,MAAM,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,KAAK,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QAC7F,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,6EAA6E;QAC7E,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACvE,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,YAAY,EAAE;gBACtE,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;aAClG;YACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACxF,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YAC9D,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,sCAAsC,CAAC,CAAC;QACjE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAChF,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kCAAkC,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,2DAA2D;QAC3D,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC7D,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAgB,EAAE;gBAChD,EAAE,OAAO,EAAE,UAAU,EAAE;gBACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,cAAc,EAAE;gBACxE,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE;aACjI;YACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACxF,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YACpE,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,qDAAqD,CAAC,CAAC;QAChF,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,0BAA0B,CAAC,CAAC;QAC1E,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAChF,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,mCAAmC,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,wEAAwE;QACxE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,kBAAkB;YAC3B,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE,eAAe;oBACxB,MAAM,EAAE,OAAgB;oBACxB,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE;iBACpD;aACJ;YACD,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,uBAAuB,EAAE;gBACpC,EAAE,OAAO,EAAE,2BAA2B,EAAE;gBACxC,EAAE,OAAO,EAAE,qBAAqB,EAAE,OAAO,EAAE,eAAe,EAAE;gBAC5D,EAAE,OAAO,EAAE,4BAA4B,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,aAAa,EAAE;gBACxF,EAAE,OAAO,EAAE,qBAAqB,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,2BAA2B,CAAC,EAAE,SAAS,EAAE,uBAAuB,EAAE,OAAO,EAAE,0BAA0B,EAAE;aACtL;YACD,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,8BAA8B,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;aAC/F;YACD,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YAC9D,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,6BAA6B,CAAC,CAAC;QACxD,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,yCAAyC,CAAC,CAAC;QAC5F,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,0CAA0C,CAAC,CAAC;QAC3F,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,0BAA0B,CAAC,EAAE,iCAAiC,CAAC,CAAC;QAC/F,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,2BAA2B,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,+EAA+E;QAC/E,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,IAAI,EAAE;YACtD,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,EAAE;oBACnD,MAAM,EAAE,MAAe;oBACvB,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE;iBAC1D;aACJ;YACD,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,SAAS,EAAE;gBACtB,EAAE,OAAO,EAAE,aAAa,EAAE;gBAC1B,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,aAAa,EAAE;gBAC1E,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,eAAe,EAAE;gBACxE,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE;gBACzI,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,oBAAoB,EAAE;aACzI;YACD,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;aACjF;YACD,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YAC9D,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,qCAAqC,CAAC,CAAC;QAChE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,2BAA2B,CAAC,CAAC;QAC5E,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAChF,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAAE,iCAAiC,CAAC,CAAC;QACxF,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,kCAAkC,CAAC,CAAC;QAC1F,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACzE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,qEAAqE;QACrE,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACvE,MAAM,IAAI,GAAG;YACT,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,OAAO,EAAE,UAAU,EAAE;gBACvB,EAAE,OAAO,EAAE,WAAW,EAAE;gBACxB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;gBAC5H,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE;aAC7H;YACD,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;aAC9E;YACD,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YAC9D,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QAEvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,4CAA4C,CAAC,CAAC;QACvE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAChF,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACvE,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,+BAA+B,CAAC,CAAC;QAC9E,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,4BAA4B,CAAC,EAAE,yDAAyD,CAAC,CAAC;QACzH,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,eAAe;QACf,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,mBAAmB,WAAW,IAAI,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjF,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAChE,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;YAAS,CAAC;QACP,MAAM,IAAA,sBAAO,GAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}