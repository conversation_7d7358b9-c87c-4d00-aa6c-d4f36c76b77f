{"version": 3, "file": "test_window_functions_improved.js", "sourceRoot": "", "sources": ["../../tests/test_window_functions_improved.ts"], "names": [], "mappings": ";;AAAA,uEAAuE;AACvE,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;AAE3D,4DAA4D;AAC5D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAClD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE;QACnB,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE;QACjI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;QACtK,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAe,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE;QAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAgB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;KAClI;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;AAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,qFAAqF,CAAC,CAAC;AAEnG,+DAA+D;AAC/D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACrD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE;QACnB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;QAC/F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE;QACpI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;KAC1G;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;AAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC,CAAC;AAEvF,+DAA+D;AAC/D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACrD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE;QACrI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;QAC1K,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE;KAC5I;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;AAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,kGAAkG,CAAC,CAAC;AAEhH,kEAAkE;AAClE,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;AACxD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;QACnG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,mBAAmB,EAAE;QAC5G,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,OAAO,EAAE,eAAe,EAAE;KAC/E;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;AAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,GAAG,CAAC,2FAA2F,CAAC,CAAC;AAEzG,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;AAE/D,oBAAoB;AACpB,IAAA,sBAAO,GAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC"}