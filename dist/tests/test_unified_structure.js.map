{"version": 3, "file": "test_unified_structure.js", "sourceRoot": "", "sources": ["../../tests/test_unified_structure.ts"], "names": [], "mappings": ";AAAA,+CAA+C;;AAE/C,8DAA2E;AAE3E,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;AAErE,CAAC,KAAK,IAAI,EAAE;IACV,IAAI,CAAC;QACH,mDAAmD;QACnD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,MAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,UAAU,EAAE,EAAqB,uBAAuB;gBACjE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAoB,wBAAwB;gBAClE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAyB,mBAAmB;aAC9D;YACD,KAAK,EAAE,CAAC;SACT,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,6BAAU,EAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,oDAAoD;QACpD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,MAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,UAAU,EAAE,EAAqC,YAAY;gBACtE,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,EAAG,sBAAsB;gBAChF,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,EAA2B,wBAAwB;gBACjF,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,EAAc,aAAa;gBACtE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,CAA0B,yBAAyB;aACnF;YACD,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE;aAC7D;YACD,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;YACtD,KAAK,EAAE,CAAC;SACT,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,6BAAU,EAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,8CAA8C;QAC9C,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,CAAwB,4BAA4B;aACvF;YACD,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE;gBAC9C,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,QAAQ,EAAE;aACnD;YACD,KAAK,EAAE,CAAC;SACT,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,6BAAU,EAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,6CAA6C;QAC7C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,MAAM,EAAE;gBACjB,EAAE,KAAK,EAAE,WAAW,EAAE;gBACtB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE;gBAC3G,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;gBAC3G,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;aACzG;YACD,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;aAClD;YACD,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YAC7B,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,6BAAU,EAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,mCAAmC;QACnC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,OAAO,GAAgB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EAAuB,yBAAyB;gBACnF,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,EAA0B,0BAA0B;gBACnF,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,EAAuB,4BAA4B;gBACrF,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,CAA0B,yBAAyB;aACnF;YACD,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;YACnC,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,MAAM,EAAE,GAAG,IAAA,6BAAU,EAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACxF,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;YAAS,CAAC;QACT,MAAM,IAAA,0BAAO,GAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,EAAE,CAAC"}