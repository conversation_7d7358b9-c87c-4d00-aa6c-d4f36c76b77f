"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test window functions without GROUP BY (no dimensions)
const queryBuilder_1 = require("../src/queryBuilder");
let cfg = {
    "table": "progress_history",
    "columns": [
        {
            "field": "Work done",
            "agg": "cumsum",
            "orderBy": "Date",
            "alias": "running_total"
        },
        {
            "field": "Work done",
            "agg": "moving_avg",
            "orderBy": "Date",
            "frame": { "preceding": 2 },
            "alias": "moving_avg"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "Date", "direction": "asc" }
    ],
    "limit": 5
};
(async () => {
    try {
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        console.log('Generated SQL (no GROUP BY):');
        console.log(q.toSQL().toNative());
        const result = await q;
        console.table(result);
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
    }
})();
//# sourceMappingURL=test_no_groupby.js.map