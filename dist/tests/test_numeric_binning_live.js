"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test numeric binning with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Numeric Binning with Live Data ===\n');
(async () => {
    try {
        // Test 1: Basic numeric binning with 10 bins
        console.log('1. Scope with 10 bins:');
        let cfg1 = {
            "table": "progress_history",
            "columns": [
                { "field": "Scope", "binCount": 10 },
                { "field": "Date", "agg": "count", "alias": "record_count" },
                { "field": "Work done", "agg": "sum", "alias": "total_work" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
            ],
            "orderBy": [
                { "column": "scope_bin_10", "direction": "asc" }
            ],
            "limit": 10
        };
        const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
        console.log('Generated SQL:');
        console.log(q1.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows1 = await q1;
        console.table(rows1);
        // Test 2: Mixed date and numeric binning
        console.log('\n\n2. Mixed Date (month) and Numeric (Scope with 5 bins):');
        let cfg2 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date", "dateBin": "month" },
                { "field": "Scope", "binCount": 5 },
                { "field": "Work done", "agg": "sum", "alias": "monthly_work" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
            ],
            "orderBy": [
                { "column": "date_month", "direction": "asc" },
                { "column": "scope_bin_5", "direction": "asc" }
            ],
            "limit": 15
        };
        const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
        console.log('Generated SQL:');
        console.log(q2.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows2 = await q2;
        console.table(rows2);
        // Test 3: Custom alias
        console.log('\n\n3. Custom Alias for Scope Binning:');
        let cfg3 = {
            "table": "progress_history",
            "columns": [
                { "field": "Scope", "binCount": 8, "alias": "scope_buckets" },
                { "field": "Date", "agg": "count", "alias": "bucket_count" },
                { "field": "Scope", "agg": "avg", "alias": "avg_scope" }
            ],
            "filters": [
                { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
            ],
            "orderBy": [
                { "column": "scope_buckets", "direction": "asc" }
            ],
            "limit": 10
        };
        const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
        console.log('Generated SQL:');
        console.log(q3.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows3 = await q3;
        console.table(rows3);
        console.log('\n=== Numeric Binning Live Test Complete ===');
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
//# sourceMappingURL=test_numeric_binning_live.js.map