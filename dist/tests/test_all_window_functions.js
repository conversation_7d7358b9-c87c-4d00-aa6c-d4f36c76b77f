"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test all window functions in both GROUP BY and non-GROUP BY scenarios
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Window Functions ===\n');
// Test 1: With GROUP BY (dimensions present)
console.log('1. WITH GROUP BY (dimensions present):');
let cfgWithGroupBy = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],
    "measures": [
        { "fn": "cumsum", "field": "Work done", "partitionBy": ["Sublayer"], "orderBy": "Date", "alias": "cumsum_test" },
        { "fn": "moving_avg", "field": "Work done", "orderBy": "Date", "frame": { "preceding": 2, "following": null }, "alias": "moving_avg_test" },
        { "fn": "moving_sum", "field": "Work done", "orderBy": "Date", "frame": { "preceding": 1, "following": null }, "alias": "moving_sum_test" },
        { "fn": "delta", "field": "Work done", "orderBy": "Date", "alias": "delta_test" },
        { "fn": "pct_change", "field": "Work done", "orderBy": "Date", "alias": "pct_change_test" },
        { "fn": "rank", "field": "Work done", "orderBy": "Work done", "alias": "rank_test" },
        { "fn": "dense_rank", "field": "Work done", "orderBy": "Work done", "alias": "dense_rank_test" },
        { "fn": "row_number", "field": "Work done", "orderBy": "Date", "alias": "row_number_test" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 3
};
const qWithGroupBy = (0, queryBuilder_1.buildQuery)(cfgWithGroupBy);
console.log('SQL:', qWithGroupBy.toSQL().sql);
console.log('Expected: Uses sum(sum("Work done")), avg(sum("Work done")), etc.\n');
// Test 2: Without GROUP BY (no dimensions)
console.log('2. WITHOUT GROUP BY (no dimensions):');
let cfgWithoutGroupBy = {
    "table": "progress_history",
    "dimensions": [], // No dimensions
    "measures": [
        { "fn": "cumsum", "field": "Work done", "orderBy": "Date", "alias": "cumsum_test" },
        { "fn": "moving_avg", "field": "Work done", "orderBy": "Date", "frame": { "preceding": 2, "following": null }, "alias": "moving_avg_test" },
        { "fn": "moving_sum", "field": "Work done", "orderBy": "Date", "frame": { "preceding": 1, "following": null }, "alias": "moving_sum_test" },
        { "fn": "delta", "field": "Work done", "orderBy": "Date", "alias": "delta_test" },
        { "fn": "pct_change", "field": "Work done", "orderBy": "Date", "alias": "pct_change_test" },
        { "fn": "rank", "field": "Work done", "orderBy": "Work done", "alias": "rank_test" },
        { "fn": "dense_rank", "field": "Work done", "orderBy": "Work done", "alias": "dense_rank_test" },
        { "fn": "row_number", "field": "Work done", "orderBy": "Date", "alias": "row_number_test" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 3
};
const qWithoutGroupBy = (0, queryBuilder_1.buildQuery)(cfgWithoutGroupBy);
console.log('SQL:', qWithoutGroupBy.toSQL().sql);
console.log('Expected: Uses sum("Work done"), avg("Work done"), etc. (no nested aggregation)\n');
console.log('=== Test Complete ===');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=test_all_window_functions.js.map