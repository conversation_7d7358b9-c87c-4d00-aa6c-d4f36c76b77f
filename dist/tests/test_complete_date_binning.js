"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test all date binning options with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing All Date Binning Options with Live Data ===\n');
const allDateBinOptions = [
    { bin: 'day', description: 'Calendar Day' },
    { bin: 'week', description: 'Calendar Week' },
    { bin: 'month', description: 'Calendar Month' },
    { bin: 'quarter', description: 'Calendar Quarter' },
    { bin: 'year', description: 'Calendar Year' },
    { bin: 'day_of_week', description: 'Day of Week (0=Sunday, 6=Saturday)' },
    { bin: 'day_of_month', description: 'Day of Month (1-31)' },
    { bin: 'day_of_year', description: 'Day of Year (1-366)' },
    { bin: 'week_of_year', description: 'Week of Year (1-53)' },
    { bin: 'month_of_year', description: 'Month of Year (1-12)' },
    { bin: 'quarter_of_year', description: 'Quarter of Year (1-4)' },
    { bin: 'year_of_era', description: 'Year of Era (Full Year)' },
    { bin: 'dont_bin', description: 'No Binning (Raw Date)' }
];
(async () => {
    try {
        for (const option of allDateBinOptions) {
            console.log(`\n--- ${option.description} ---`);
            const cfg = {
                table: "progress_history",
                dimensions: [
                    { field: "Date", dateBin: option.bin }
                ],
                measures: [
                    { agg: "sum", field: "Work done", alias: "total_work" },
                    { agg: "count", field: "Date", alias: "record_count" }
                ],
                filters: [
                    { field: "Subactivity", op: "eq", value: "Piles Installed" }
                ],
                orderBy: [
                    { field: option.bin === "dont_bin" ? "Date" : `Date_${option.bin}`, direction: "asc" }
                ],
                limit: 5
            };
            const q = (0, queryBuilder_1.buildQuery)(cfg);
            console.log(`SQL: ${q.toSQL().sql}`);
            const rows = await q;
            console.table(rows);
        }
        console.log('\n=== All Date Binning Options Test Complete ===');
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
//# sourceMappingURL=test_complete_date_binning.js.map