"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test more date binning options with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing More Date Binning Options ===\n');
// Test 1: Day of week analysis
console.log('1. Day of Week Analysis:');
let cfg1 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "day_of_week", "alias": "weekday" },
        { "field": "Work done", "agg": "sum", "alias": "total_work" },
        { "field": "Date", "agg": "count", "alias": "record_count" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "orderBy": [{ "column": "weekday", "direction": "asc" }],
    "limit": 10
};
// Test 2: Quarter analysis
console.log('2. Quarterly Analysis:');
let cfg2 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "quarter" },
        { "field": "Scope", "agg": "sum", "alias": "quarterly_scope" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 10
};
(async () => {
    try {
        console.log('\n--- Day of Week Results ---');
        const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;
        console.table(rows1);
        console.log('\n--- Quarterly Results ---');
        const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;
        console.table(rows2);
    }
    catch (error) {
        console.error('Error:', error);
    }
})();
//# sourceMappingURL=test_more_date_bins.js.map