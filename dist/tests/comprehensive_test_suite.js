"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Comprehensive Test Suite - Runs all tests in a single command
const queryBuilder_1 = require("../src/queryBuilder");
class TestRunner {
    constructor() {
        this.results = [];
    }
    async runTest(name, testFn) {
        const startTime = Date.now();
        try {
            await testFn();
            this.results.push({
                name,
                passed: true,
                duration: Date.now() - startTime
            });
            console.log(`✅ ${name} - PASSED (${Date.now() - startTime}ms)`);
        }
        catch (error) {
            this.results.push({
                name,
                passed: false,
                error: error instanceof Error ? error.message : String(error),
                duration: Date.now() - startTime
            });
            console.log(`❌ ${name} - FAILED (${Date.now() - startTime}ms)`);
            console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    printSummary() {
        const passed = this.results.filter(r => r.passed).length;
        const total = this.results.length;
        const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
        console.log('\n' + '='.repeat(60));
        console.log('🎯 TEST SUMMARY');
        console.log('='.repeat(60));
        console.log(`✅ Passed: ${passed}/${total} tests`);
        console.log(`⏱️  Total Time: ${totalTime}ms`);
        console.log(`📊 Success Rate: ${Math.round((passed / total) * 100)}%`);
        if (passed === total) {
            console.log('🎉 ALL TESTS PASSED! 🎉');
        }
        else {
            console.log('\n❌ Failed Tests:');
            this.results.filter(r => !r.passed).forEach(r => {
                console.log(`   • ${r.name}: ${r.error}`);
            });
        }
        console.log('='.repeat(60));
        return passed === total;
    }
}
async function runComprehensiveTestSuite() {
    console.log('🚀 COMPREHENSIVE QUERY BUILDER TEST SUITE 🚀\n');
    const runner = new TestRunner();
    // Test 1: Basic Window Functions
    await runner.runTest('Window Functions with GROUP BY', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Date" },
                { field: "Sublayer" },
                { field: "Work done", agg: "sum", alias: "total_work" },
                { field: "Work done", agg: "cumsum", partitionBy: ["Sublayer"], orderBy: "Date", alias: "cumsum_partitioned" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            orderBy: [{ column: "Date", direction: "asc" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('cumsum_partitioned'))
            throw new Error('Missing cumsum column');
    });
    // Test 2: Date Binning
    await runner.runTest('Date Binning', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" },
                { field: "Work done", agg: "sum", alias: "monthly_work" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('date_month'))
            throw new Error('Missing date_month column');
    });
    // Test 3: Numeric Binning
    await runner.runTest('Numeric Binning', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Scope", binCount: 10 },
                { field: "Work done", agg: "sum", alias: "total_work" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('scope_bin_10'))
            throw new Error('Missing scope_bin_10 column');
    });
    // Test 4: JOINs with Window Functions
    await runner.runTest('JOINs with Window Functions', async () => {
        const cfg = {
            table: "progress_history",
            joins: [{
                    table: "progress_data",
                    type: "inner",
                    on: { left: "Sublayer", right: "Sublayer" }
                }],
            columns: [
                { field: "progress_history.Date" },
                { field: "progress_history.Sublayer" },
                { field: "progress_data.Scope", alias: "planned_scope" },
                { field: "progress_history.Work done", agg: "sum", alias: "actual_work" },
                { field: "progress_data.Scope", agg: "cumsum", partitionBy: ["progress_history.Sublayer"], orderBy: "progress_history.Date", alias: "cumulative_planned" }
            ],
            filters: [{ field: "progress_history.Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('planned_scope'))
            throw new Error('Missing planned_scope column');
        if (!rows[0].hasOwnProperty('cumulative_planned'))
            throw new Error('Missing cumulative_planned column');
    });
    // Test 5: Expression Filters
    await runner.runTest('Expression Filters', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { field: "Work done", agg: "count", alias: "record_count" }
            ],
            filters: [
                { expr: 'EXTRACT(YEAR FROM "Date") = 2025' }
            ],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
    });
    // Test 6: Complex Mixed Query
    await runner.runTest('Complex Mixed Query', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" },
                { field: "Scope", binCount: 5 },
                { field: "Work done", agg: "sum", alias: "total_work" },
                { field: "Work done", agg: "cumsum", orderBy: "date_month", alias: "running_total" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 10
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('date_month'))
            throw new Error('Missing date_month column');
        if (!rows[0].hasOwnProperty('scope_bin_5'))
            throw new Error('Missing scope_bin_5 column');
        if (!rows[0].hasOwnProperty('running_total'))
            throw new Error('Missing running_total column');
    });
    // Test 7: No GROUP BY Scenario
    await runner.runTest('No GROUP BY Scenario', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Date" },
                { field: "Sublayer" },
                { field: "Work done" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('date'))
            throw new Error('Missing date column');
        if (!rows[0].hasOwnProperty('sublayer'))
            throw new Error('Missing sublayer column');
        if (!rows[0].hasOwnProperty('work_done'))
            throw new Error('Missing work_done column');
    });
    // Test 8: Multiple Window Functions
    await runner.runTest('Multiple Window Functions', async () => {
        const cfg = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { field: "Work done", agg: "sum", alias: "total_work" },
                { field: "Work done", agg: "cumsum", partitionBy: ["Sublayer"], alias: "cumsum_by_sublayer" },
                { field: "Work done", agg: "avg", alias: "avg_work" }
            ],
            filters: [{ field: "Subactivity", op: "eq", value: "Piles Installed" }],
            limit: 5
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        const rows = await q;
        if (rows.length === 0)
            throw new Error('No results returned');
        if (!rows[0].hasOwnProperty('cumsum_by_sublayer'))
            throw new Error('Missing cumsum_by_sublayer column');
    });
    const allPassed = runner.printSummary();
    // Clean up
    await (0, queryBuilder_1.cleanup)();
    // Exit with appropriate code
    process.exit(allPassed ? 0 : 1);
}
// Run the test suite
runComprehensiveTestSuite().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
});
//# sourceMappingURL=comprehensive_test_suite.js.map