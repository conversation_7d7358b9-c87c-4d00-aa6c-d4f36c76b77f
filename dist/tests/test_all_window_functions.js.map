{"version": 3, "file": "test_all_window_functions.js", "sourceRoot": "", "sources": ["../../tests/test_all_window_functions.ts"], "names": [], "mappings": ";;AAAA,wEAAwE;AACxE,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAElD,6CAA6C;AAC7C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;AACtD,IAAI,cAAc,GAAG;IACjB,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE;QACnB,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;QAC1H,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;QAC3F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;QACrG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAe,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;QAC9F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC1G,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;KACxG;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,YAAY,GAAG,IAAA,yBAAU,EAAC,cAAc,CAAC,CAAC;AAChD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AAC9C,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;AAEnF,2CAA2C;AAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACpD,IAAI,iBAAiB,GAAG;IACpB,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;QAC7F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;QAC3F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;QACrG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAe,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;QAC9F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC1G,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;KACxG;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IACxF,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,eAAe,GAAG,IAAA,yBAAU,EAAC,iBAAiB,CAAC,CAAC;AACtD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACjD,OAAO,CAAC,GAAG,CAAC,mFAAmF,CAAC,CAAC;AAEjG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAErC,oBAAoB;AACpB,IAAA,sBAAO,GAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC"}