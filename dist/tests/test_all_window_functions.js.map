{"version": 3, "file": "test_all_window_functions.js", "sourceRoot": "", "sources": ["../../tests/test_all_window_functions.ts"], "names": [], "mappings": ";;AAAA,wEAAwE;AACxE,sDAAuE;AAEvE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAElD,6CAA6C;AAC7C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;AACtD,IAAI,cAAc,GAAG;IACjB,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;IAClC,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;QAChH,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3I,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3I,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;QACjF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3F,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;QACpF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAChG,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9F;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC/E,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,YAAY,GAAG,IAAA,yBAAU,EAAC,cAA6B,CAAC,CAAC;AAC/D,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AAC9C,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;AAEnF,2CAA2C;AAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACpD,IAAI,iBAAiB,GAAG;IACpB,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,EAAE,EAAE,gBAAgB;IAClC,UAAU,EAAE;QACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;QACnF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3I,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3I,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;QACjF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAC3F,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;QACpF,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAChG,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9F;IACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC/E,OAAO,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,eAAe,GAAG,IAAA,yBAAU,EAAC,iBAAgC,CAAC,CAAC;AACrE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACjD,OAAO,CAAC,GAAG,CAAC,mFAAmF,CAAC,CAAC;AAEjG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAErC,oBAAoB;AACpB,IAAA,sBAAO,GAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC"}