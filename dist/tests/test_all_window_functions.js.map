{"version": 3, "file": "test_all_window_functions.js", "sourceRoot": "", "sources": ["../../tests/test_all_window_functions.ts"], "names": [], "mappings": ";;AAAA,wEAAwE;AACxE,sDAA0D;AAE1D,KAAK,UAAU,yBAAyB;IACpC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAElD,6CAA6C;IAC7C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,IAAI,cAAc,GAAG;QACjB,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE;YACP,EAAE,OAAO,EAAE,MAAM,EAAE;YACnB,EAAE,OAAO,EAAE,UAAU,EAAE;YACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;YAC1H,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;YAC3F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;YACrG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAe,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;YAC9F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAC1G,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;SACxG;QACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;QACxF,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,MAAM,YAAY,GAAG,IAAA,yBAAU,EAAC,cAAc,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;IAEnF,oBAAoB;IACpB,IAAI,CAAC;QACD,MAAM,eAAe,GAAG,MAAM,YAAY,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,eAAe,eAAe,CAAC,MAAM,OAAO,CAAC,CAAC;QAC1D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QACpE,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACtG,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,2CAA2C;IAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,IAAI,iBAAiB,GAAG;QACpB,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE;YACP,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,QAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;YAC7F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAClI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;YAC3F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;YACrG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAe,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE;YAC9F,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAC1G,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,YAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;SACxG;QACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;QACxF,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,MAAM,eAAe,GAAG,IAAA,yBAAU,EAAC,iBAAiB,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,mFAAmF,CAAC,CAAC;IAEjG,oBAAoB;IACpB,IAAI,CAAC;QACD,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,eAAe,kBAAkB,CAAC,MAAM,OAAO,CAAC,CAAC;QAC7D,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QACvE,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1G,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAErC,oBAAoB;IACpB,MAAM,IAAA,sBAAO,GAAE,CAAC;AACpB,CAAC;AAED,eAAe;AACf,yBAAyB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAClE,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}