{"version": 3, "file": "test_numeric_binning.js", "sourceRoot": "", "sources": ["../../tests/test_numeric_binning.ts"], "names": [], "mappings": ";;AAAA,qCAAqC;AACrC,sDAA0D;AAE1D,KAAK,UAAU,qBAAqB;IAChC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,0DAA0D;IAC1D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;IAEpC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,2BAA2B,CAAC,CAAC;QAE1D,IAAI,GAAG,GAAG;YACN,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE;gBACP,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE;gBAC1C,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAgB,EAAE,OAAO,EAAE,cAAc,EAAE;gBACrE,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,YAAY,EAAE;aACzE;YACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACxF,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,QAAQ,EAAE,EAAE,WAAW,EAAE,KAAc,EAAE,CAAC;YAC/E,OAAO,EAAE,EAAE;SACd,CAAC;QAEF,MAAM,CAAC,GAAG,IAAA,yBAAU,EAAC,GAAG,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAEnC,oBAAoB;QACpB,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,uCAAuC,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;YACpF,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9G,CAAC;IACL,CAAC;IAED,gDAAgD;IAChD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,IAAI,IAAI,GAAG;QACP,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE;YACP,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE;YACpC,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAgB,EAAE,OAAO,EAAE,cAAc,EAAE;SACxE;QACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;QACxF,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAEpC,oBAAoB;IACpB,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC;QAChD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9G,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,kDAAkD;IAClD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,IAAI,IAAI,GAAG;QACP,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE;YACP,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAgB,EAAE;YAChD,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE;YACpC,EAAE,OAAO,EAAE,UAAU,EAAE;YACvB,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAc,EAAE,OAAO,EAAE,YAAY,EAAE;SACzE;QACD,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;QACxF,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAEpC,oBAAoB;IACpB,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC;QAChD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3G,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,2CAA2C;IAC3C,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,IAAI,IAAI,GAAG;QACP,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE;YACP,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE;YAC9D,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAgB,EAAE,OAAO,EAAE,cAAc,EAAE;SACxE;QACD,OAAO,EAAE,CAAC;KACb,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAEpC,oBAAoB;IACpB,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC;QAChD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1G,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,oBAAoB;IACpB,MAAM,IAAA,sBAAO,GAAE,CAAC;AACpB,CAAC;AAED,eAAe;AACf,qBAAqB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAC9D,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}