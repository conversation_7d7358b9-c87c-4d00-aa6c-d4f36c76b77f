"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test date binning functionality
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Date Binning ===\n');
// Test 1: Basic date binning options
console.log('1. Basic Date Binning Options:');
let cfg1 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" },
        "Sublayer" // Mix of old and new format
    ],
    "measures": [
        { "agg": "sum", "field": "Work done", "alias": "total_work" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 5
};
const q1 = (0, queryBuilder_1.buildQueryAuto)(cfg1);
console.log('Month binning SQL:', q1.toSQL().sql);
console.log('Expected: DATE_TRUNC(\'month\', "Date") AS "Date_month"\n');
// Test 2: Different date binning options
console.log('2. Various Date Binning Options:');
const dateBinOptions = ['day', 'week', 'month', 'quarter', 'year', 'day_of_week', 'month_of_year'];
dateBinOptions.forEach(binOption => {
    let cfg = {
        "table": "progress_history",
        "dimensions": [
            { "field": "Date", "dateBin": binOption }
        ],
        "measures": [
            { "agg": "count", "field": "Work done", "alias": "count_records" }
        ],
        "limit": 3
    };
    const q = (0, queryBuilder_1.buildQueryAuto)(cfg);
    console.log(`${binOption}:`, q.toSQL().sql);
});
console.log('\n3. Custom alias with date binning:');
let cfg3 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "quarter", "alias": "reporting_quarter" }
    ],
    "measures": [
        { "agg": "sum", "field": "Scope", "alias": "total_scope" }
    ],
    "limit": 5
};
const q3 = (0, queryBuilder_1.buildQueryAuto)(cfg3);
console.log('Custom alias SQL:', q3.toSQL().sql);
console.log('Expected: Uses "reporting_quarter" as alias\n');
console.log('=== Date Binning Test Complete ===');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=test_date_binning.js.map