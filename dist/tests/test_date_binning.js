"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test date binning functionality
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Date Binning ===\n');
// Test 1: Basic date binning options
console.log('1. Basic Date Binning Options:');
let cfg1 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "month" },
        { "field": "Sublayer" },
        { "field": "Work done", "agg": "sum", "alias": "total_work" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 5
};
const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
console.log('Month binning SQL:', q1.toSQL().sql);
console.log('Expected: DATE_TRUNC(\'month\', "Date") AS "Date_month"\n');
// Test 2: Different date binning options
console.log('2. Various Date Binning Options:');
const dateBinOptions = ['day', 'week', 'month', 'quarter', 'year', 'day_of_week', 'month_of_year'];
dateBinOptions.forEach(binOption => {
    let cfg = {
        "table": "progress_history",
        "columns": [
            { "field": "Date", "dateBin": binOption },
            { "field": "Work done", "agg": "count", "alias": "count_records" }
        ],
        "limit": 3
    };
    const q = (0, queryBuilder_1.buildQuery)(cfg);
    console.log(`${binOption}:`, q.toSQL().sql);
});
console.log('\n3. Custom alias with date binning:');
let cfg3 = {
    "table": "progress_history",
    "columns": [
        { "field": "Date", "dateBin": "quarter", "alias": "reporting_quarter" },
        { "field": "Scope", "agg": "sum", "alias": "total_scope" }
    ],
    "limit": 5
};
const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
console.log('Custom alias SQL:', q3.toSQL().sql);
console.log('Expected: Uses "reporting_quarter" as alias\n');
console.log('=== Date Binning Test Complete ===');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=test_date_binning.js.map