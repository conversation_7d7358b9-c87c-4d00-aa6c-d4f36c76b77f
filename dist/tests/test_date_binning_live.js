"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test date binning with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Date Binning with Live Data ===\n');
// Test monthly aggregation
let cfg = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" },
        "Sublayer"
    ],
    "measures": [
        { "agg": "sum", "field": "Work done", "alias": "monthly_work" },
        { "agg": "sum", "field": "Scope", "alias": "monthly_scope" }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date_month", "direction": "asc" },
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 20
};
(async () => {
    try {
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative());
        console.log('\nExecuting query...');
        const rows = await q;
        console.table(rows);
    }
    catch (error) {
        console.error('Error:', error);
    }
})();
//# sourceMappingURL=test_date_binning_live.js.map