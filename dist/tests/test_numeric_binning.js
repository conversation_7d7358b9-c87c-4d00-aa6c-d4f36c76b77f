"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test numeric binning functionality
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Numeric Binning ===\n');
// Test 1: Basic numeric binning with different bin counts
console.log('1. Basic Numeric Binning Options:');
const binCounts = [10, 20, 50, 100];
binCounts.forEach(binCount => {
    console.log(`\n--- ${binCount} bins for "Work done" ---`);
    let cfg = {
        "table": "progress_history",
        "dimensions": [
            { "field": "Scope", "binCount": binCount }
        ],
        "measures": [
            { "agg": "count", "field": "Date", "alias": "record_count" },
            { "agg": "sum", "field": "Work done", "alias": "total_work" }
        ],
        "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
        "orderBy": [{ "field": `Scope_bin_${binCount}`, "direction": "asc" }],
        "limit": 10
    };
    const q = (0, queryBuilder_1.buildQueryAuto)(cfg);
    console.log('SQL:', q.toSQL().sql);
});
// Test 2: Multiple numeric columns with binning
console.log('\n\n2. Multiple Numeric Columns with Binning:');
let cfg2 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Scope", "binCount": 20 }
    ],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "record_count" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 5
};
const q2 = buildQuery(cfg2);
console.log('SQL:', q2.toSQL().sql);
// Test 3: Mix of date binning and numeric binning
console.log('\n\n3. Mixed Date and Numeric Binning:');
let cfg3 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" },
        { "field": "Scope", "binCount": 10 },
        "Sublayer" // Regular dimension
    ],
    "measures": [
        { "agg": "sum", "field": "Work done", "alias": "total_work" }
    ],
    "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
    "limit": 5
};
const q3 = buildQuery(cfg3);
console.log('SQL:', q3.toSQL().sql);
// Test 4: Custom alias for numeric binning
console.log('\n\n4. Custom Alias for Numeric Binning:');
let cfg4 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Scope", "binCount": 50, "alias": "scope_buckets" }
    ],
    "measures": [
        { "agg": "count", "field": "Date", "alias": "bucket_count" }
    ],
    "limit": 5
};
const q4 = buildQuery(cfg4);
console.log('SQL:', q4.toSQL().sql);
console.log('\n=== Numeric Binning Test Complete ===');
// Clean up and exit
(0, queryBuilder_1.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=test_numeric_binning.js.map