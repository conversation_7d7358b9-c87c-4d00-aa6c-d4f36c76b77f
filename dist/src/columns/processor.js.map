{"version": 3, "file": "processor.js", "sourceRoot": "", "sources": ["../../../src/columns/processor.ts"], "names": [], "mappings": ";AAAA,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvC,wCA8CC;AAwFD,0DAQC;AAKD,oDAEC;AAKD,gDAEC;AAKD,8CAIC;AA3LD,gDAA+B;AAC/B,iDAAmC;AAEnC,gDAM2B;AAC3B,oDAAiG;AACjG,kDAAwD;AACxD,+CAA0D;AAC1D,qDAAgE;AAChE,iDAAwD;AACxD,uDAA8D;AAE9D,MAAM,IAAI,GAAG,IAAA,cAAW,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AAE3E;;GAEG;AACH,SAAgB,cAAc,CAC5B,OAAiB,EACjB,SAAyB,EACzB,KAAsB,EACtB,cAAwB,EACxB,UAAoB;IAEpB,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1D,MAAM,gBAAgB,GAAsB,EAAE,CAAC;IAE/C,uDAAuD;IACvD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,IAAA,yBAAc,EAAC,MAAM,CAAC,CAAC;QAEvB,MAAM,KAAK,GAAG,IAAA,8BAAmB,EAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,YAAY,GAAG,IAAA,6BAAkB,EAAC,MAAM,CAAC,CAAC;QAChD,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,IAAA,2BAAgB,EAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAElE,6DAA6D;QAC7D,MAAM,eAAe,GAAoB;YACvC,GAAG,MAAM;YACT,KAAK;YACL,YAAY;YACZ,gBAAgB,EAAE,UAAU;YAC5B,aAAa,EAAE,EAAE,CAAC,gCAAgC;SACnD,CAAC;QAEF,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1D,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;QAClD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAE9B,uDAAuD;QACvD,MAAM,OAAO,GAAsB;YACjC,cAAc;YACd,MAAM,EAAE,SAAS;YACjB,UAAU,EAAE,UAAU,IAAI,KAAK;YAC/B,gBAAgB,EAAE,gBAAgB;SACnC,CAAC;QAEF,eAAe,CAAC,aAAa,GAAG,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CACxB,MAAc,EACd,OAA0B,EAC1B,MAAwB;IAExB,IAAI,cAAsB,CAAC;IAE3B,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,8BAA8B;QAC9B,cAAc,GAAG,IAAA,wBAAe,EAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;SAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACxB,oBAAoB;QACpB,cAAc,GAAG,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAE3E,kCAAkC;QAClC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,cAAc,GAAG,IAAA,uCAAyB,EAAC,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7E,CAAC;QAED,qCAAqC;QACrC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,cAAc,GAAG,IAAA,6CAA4B,EAAC,cAAc,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,iCAAiC;IACjC,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,IAAA,2BAAgB,EAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,cAAc,GAAG,IAAA,2CAAyB,EACxC,MAAM,CAAC,GAAG,EACV,cAAc,EACd,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,KAAK,EACZ,OAAO,CACR,CAAC;QACJ,CAAC;aAAM,IAAI,IAAA,+BAAoB,EAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5C,cAAc,GAAG,IAAA,qCAAsB,EAAC,MAAM,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAC5B,KAAa,EACb,aAAiC,EACjC,MAAwB;IAExB,IAAI,cAAsB,CAAC;IAE3B,IAAI,aAAa,EAAE,CAAC;QAClB,iCAAiC;QACjC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,aAAa,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;QAC9F,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,UAAU,aAAa,aAAa,CAAC,CAAC;QACxD,CAAC;QACD,cAAc,GAAG,IAAA,6BAAgB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,6BAA6B;QAC7B,MAAM,SAAS,GAAG,IAAA,kCAAqB,EAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAA,8BAAiB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,UAAU,SAAS,aAAa,CAAC,CAAC;QACpD,CAAC;QACD,cAAc,GAAG,KAAK,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,yEAAyE;QACzE,cAAc,GAAG,KAAK,CAAC;IACzB,CAAC;IAED,+BAA+B;IAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC;AAC9C,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,gBAAmC;IACzE,OAAO,gBAAgB;SACpB,MAAM,CAAC,GAAG,CAAC,EAAE;QACZ,+DAA+D;QAC/D,oEAAoE;QACpE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACrD,CAAC,CAAC;SACD,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,gBAAmC;IACtE,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACxD,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,gBAAmC;IACpE,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,gBAAmC;IACnE,OAAO,gBAAgB;SACpB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,aAAa,QAAQ,GAAG,CAAC,KAAK,GAAG,CAAC;SACpD,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC"}