{"version": 3, "file": "windowFunctions.js", "sourceRoot": "", "sources": ["../../../src/columns/windowFunctions.ts"], "names": [], "mappings": ";AAAA,iCAAiC;;AAOjC,8DA+EC;AAlFD;;GAEG;AACH,SAAgB,yBAAyB,CACvC,GAAoB,EACpB,UAAkB,EAClB,WAAsB,EACtB,OAA2B,EAC3B,KAAmB,EACnB,OAA2B;IAE3B,MAAM,YAAY,GAAG,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAE5E,QAAQ,GAAG,EAAE,CAAC;QACZ,uBAAuB;QACvB,KAAK,QAAQ;YACX,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,QAAQ;YACX,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,UAAU;YACb,OAAO,SAAS,UAAU,WAAW,YAAY,GAAG,CAAC;QAEvD,KAAK,QAAQ;YACX,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,QAAQ;YACX,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,mBAAmB;QACnB,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,cAAc;YACjB,OAAO,SAAS,UAAU,WAAW,YAAY,GAAG,CAAC;QAEvD,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,uBAAuB;QACvB,KAAK,OAAO;YACV,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,OAAO,GAAG,UAAU,UAAU,UAAU,WAAW,oBAAoB,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC;QAExG,KAAK,YAAY;YACf,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClF,OAAO,YAAY,UAAU,UAAU,UAAU,WAAW,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,mBAAmB,UAAU,WAAW,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC;QAEvM,KAAK,KAAK;YACR,OAAO,OAAO,UAAU,WAAW,YAAY,GAAG,CAAC;QAErD,KAAK,MAAM;YACT,OAAO,QAAQ,UAAU,WAAW,YAAY,GAAG,CAAC;QAEtD,oBAAoB;QACpB,KAAK,MAAM;YACT,OAAO,gBAAgB,YAAY,GAAG,CAAC;QAEzC,KAAK,YAAY;YACf,OAAO,sBAAsB,YAAY,GAAG,CAAC;QAE/C,KAAK,YAAY;YACf,OAAO,sBAAsB,YAAY,GAAG,CAAC;QAE/C,KAAK,cAAc;YACjB,OAAO,wBAAwB,YAAY,GAAG,CAAC;QAEjD,KAAK,OAAO;YACV,yEAAyE;YACzE,kCAAkC;YAClC,OAAO,kBAAkB,YAAY,GAAG,CAAC;QAE3C;YACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAC3B,WAAsB,EACtB,OAA2B,EAC3B,KAAmB,EACnB,GAAqB;IAErB,MAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,eAAe;IACf,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,IAAI,CAAC,gBAAgB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED,WAAW;IACX,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/D,KAAK,CAAC,IAAI,CAAC,YAAY,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,eAAe;IACf,IAAI,KAAK,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;QACpC,MAAM,WAAW,GAAG,mBAAmB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,KAAmB,EAAE,GAAqB;IACrE,8CAA8C;IAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,IAAI,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,0BAA0B,CAAC;QACpC,CAAC;QACD,IAAI,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,0CAA0C,CAAC,CAAC,uBAAuB;QAC5E,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,KAAK,GAAa,CAAC,cAAc,CAAC,CAAC;IAEzC,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,YAAY,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAElB,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,YAAY,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5B,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,GAAqB;IAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,GAAoB;IAChD,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,GAAoB;IAC5C,OAAO,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChG,CAAC"}