{"version": 3, "file": "windowFunctions.js", "sourceRoot": "", "sources": ["../../../src/columns/windowFunctions.ts"], "names": [], "mappings": ";AAAA,iCAAiC;;AAOjC,8DAqFC;AAxFD;;GAEG;AACH,SAAgB,yBAAyB,CACvC,GAAoB,EACpB,UAAkB,EAClB,WAAsB,EACtB,OAA2B,EAC3B,KAAmB,EACnB,OAA2B;IAE3B,2EAA2E;IAC3E,MAAM,UAAU,GAAG,OAAO,EAAE,UAAU,IAAI,KAAK,CAAC;IAChD,MAAM,YAAY,GAAG,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAEjG,mFAAmF;IACnF,MAAM,cAAc,GAAG,UAAU,IAAI,uBAAuB,CAAC,GAAG,CAAC,CAAC;IAClE,MAAM,eAAe,GAAG,cAAc,CAAC,CAAC,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IAE3F,QAAQ,GAAG,EAAE,CAAC;QACZ,uBAAuB;QACvB,KAAK,QAAQ;YACX,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,QAAQ;YACX,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,UAAU;YACb,OAAO,SAAS,eAAe,WAAW,YAAY,GAAG,CAAC;QAE5D,KAAK,QAAQ;YACX,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,QAAQ;YACX,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,mBAAmB;QACnB,KAAK,YAAY;YACf,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,YAAY;YACf,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,cAAc;YACjB,OAAO,SAAS,eAAe,WAAW,YAAY,GAAG,CAAC;QAE5D,KAAK,YAAY;YACf,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,YAAY;YACf,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,uBAAuB;QACvB,KAAK,OAAO;YACV,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,OAAO,GAAG,eAAe,UAAU,eAAe,WAAW,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC;QAEvJ,KAAK,YAAY;YACf,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClF,OAAO,YAAY,eAAe,UAAU,eAAe,WAAW,oBAAoB,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,mBAAmB,eAAe,WAAW,oBAAoB,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC;QAEhS,KAAK,KAAK;YACR,OAAO,OAAO,eAAe,WAAW,YAAY,GAAG,CAAC;QAE1D,KAAK,MAAM;YACT,OAAO,QAAQ,eAAe,WAAW,YAAY,GAAG,CAAC;QAE3D,8EAA8E;QAC9E,KAAK,MAAM;YACT,OAAO,gBAAgB,YAAY,GAAG,CAAC;QAEzC,KAAK,YAAY;YACf,OAAO,sBAAsB,YAAY,GAAG,CAAC;QAE/C,KAAK,YAAY;YACf,OAAO,sBAAsB,YAAY,GAAG,CAAC;QAE/C,KAAK,cAAc;YACjB,OAAO,wBAAwB,YAAY,GAAG,CAAC;QAEjD,KAAK,OAAO;YACV,yEAAyE;YACzE,kCAAkC;YAClC,OAAO,kBAAkB,YAAY,GAAG,CAAC;QAE3C;YACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,GAAoB;IACnD,yEAAyE;IACzE,OAAO;QACL,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;QAClD,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY;QACtE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM;KACrC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,UAAkB,EAAE,GAAoB;IACnE,kEAAkE;IAClE,uDAAuD;IACvD,QAAQ,GAAG,EAAE,CAAC;QACZ,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,GAAG,CAAC;QAE9B,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,GAAG,CAAC;QAE9B,KAAK,UAAU,CAAC;QAChB,KAAK,cAAc;YACjB,OAAO,SAAS,UAAU,GAAG,CAAC;QAEhC,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,GAAG,CAAC;QAE9B,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY;YACf,OAAO,OAAO,UAAU,GAAG,CAAC;QAE9B,KAAK,OAAO,CAAC;QACb,KAAK,YAAY,CAAC;QAClB,KAAK,KAAK,CAAC;QACX,KAAK,MAAM;YACT,+CAA+C;YAC/C,OAAO,OAAO,UAAU,GAAG,CAAC;QAE9B;YACE,OAAO,OAAO,UAAU,GAAG,CAAC;IAChC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAC3B,WAAsB,EACtB,OAA2B,EAC3B,KAAmB,EACnB,GAAqB,EACrB,UAAoB,EACpB,OAA2B;IAE3B,MAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,sDAAsD;IACtD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,sBAAsB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/F,KAAK,CAAC,IAAI,CAAC,gBAAgB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,kDAAkD;IAClD,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/D,MAAM,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,sBAAsB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;QACjG,KAAK,CAAC,IAAI,CAAC,YAAY,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,eAAe;IACf,IAAI,KAAK,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;QACpC,MAAM,WAAW,GAAG,mBAAmB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,KAAmB,EAAE,GAAqB;IACrE,8CAA8C;IAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,IAAI,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,0BAA0B,CAAC;QACpC,CAAC;QACD,IAAI,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,0CAA0C,CAAC,CAAC,uBAAuB;QAC5E,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,KAAK,GAAa,CAAC,cAAc,CAAC,CAAC;IAEzC,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,YAAY,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAElB,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,YAAY,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5B,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,GAAqB;IAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,GAAoB;IAChD,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,GAAoB;IAC5C,OAAO,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChG,CAAC;AAED;;;GAGG;AACH,SAAS,sBAAsB,CAAC,GAAW,EAAE,UAAoB,EAAE,OAA2B;IAC5F,mEAAmE;IACnE,IAAI,OAAO,EAAE,gBAAgB,EAAE,CAAC;QAC9B,MAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;QAC7E,IAAI,cAAc,EAAE,CAAC;YACnB,qEAAqE;YACrE,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;gBACnD,oEAAoE;gBACpE,OAAO,yBAAyB,CAAC,qBAAqB,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9H,CAAC;YACD,yEAAyE;YACzE,IAAI,cAAc,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;gBAChD,OAAO,qBAAqB,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;IACH,CAAC;IAED,6DAA6D;IAC7D,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;QACvC,CAAC;IACH,CAAC;IAED,uCAAuC;IACvC,OAAO,IAAI,GAAG,GAAG,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,KAAa,EAAE,KAAc;IAC1D,IAAI,KAAK,EAAE,CAAC;QACV,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG,CAAC;IACjC,CAAC;IACD,OAAO,IAAI,KAAK,GAAG,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,SAAS,yBAAyB,CAAC,eAAuB,EAAE,OAAe;IACzE,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,KAAK;YACR,OAAO,QAAQ,eAAe,GAAG,CAAC;QACpC,KAAK,MAAM;YACT,OAAO,sBAAsB,eAAe,GAAG,CAAC;QAClD,KAAK,OAAO;YACV,OAAO,uBAAuB,eAAe,GAAG,CAAC;QACnD,KAAK,SAAS;YACZ,OAAO,yBAAyB,eAAe,GAAG,CAAC;QACrD,KAAK,MAAM;YACT,OAAO,sBAAsB,eAAe,GAAG,CAAC;QAClD,KAAK,aAAa;YAChB,OAAO,oBAAoB,eAAe,GAAG,CAAC;QAChD,KAAK,eAAe;YAClB,OAAO,sBAAsB,eAAe,GAAG,CAAC;QAClD,KAAK,iBAAiB;YACpB,OAAO,wBAAwB,eAAe,GAAG,CAAC;QACpD;YACE,OAAO,eAAe,CAAC;IAC3B,CAAC;AACH,CAAC"}