"use strict";
// Window function SQL generation
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateWindowFunctionSQL = generateWindowFunctionSQL;
/**
 * Generate SQL for window functions
 */
function generateWindowFunctionSQL(agg, expression, partitionBy, orderBy, frame, context) {
    // Determine if we need nested aggregation (when there's a GROUP BY clause)
    const hasGroupBy = context?.hasGroupBy || false;
    const windowClause = generateWindowClause(partitionBy, orderBy, frame, agg, hasGroupBy, context);
    // For window functions that operate on aggregated data, we need nested aggregation
    const needsNestedAgg = hasGroupBy && needsAggregationNesting(agg);
    const finalExpression = needsNestedAgg ? wrapWithAggregation(expression, agg) : expression;
    switch (agg) {
        // Cumulative functions
        case 'cumsum':
            return `SUM(${finalExpression}) OVER (${windowClause})`;
        case 'cumavg':
            return `AVG(${finalExpression}) OVER (${windowClause})`;
        case 'cumcount':
            return `COUNT(${finalExpression}) OVER (${windowClause})`;
        case 'cummax':
            return `MAX(${finalExpression}) OVER (${windowClause})`;
        case 'cummin':
            return `MIN(${finalExpression}) OVER (${windowClause})`;
        // Moving functions
        case 'moving_avg':
            return `AVG(${finalExpression}) OVER (${windowClause})`;
        case 'moving_sum':
            return `SUM(${finalExpression}) OVER (${windowClause})`;
        case 'moving_count':
            return `COUNT(${finalExpression}) OVER (${windowClause})`;
        case 'moving_max':
            return `MAX(${finalExpression}) OVER (${windowClause})`;
        case 'moving_min':
            return `MIN(${finalExpression}) OVER (${windowClause})`;
        // Analytical functions
        case 'delta':
            const deltaOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
            return `${finalExpression} - LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, deltaOrderBy, undefined, agg, hasGroupBy, context)})`;
        case 'pct_change':
            const pctOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
            return `100.0 * (${finalExpression} - LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, pctOrderBy, undefined, agg, hasGroupBy, context)})) / NULLIF(LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, pctOrderBy, undefined, agg, hasGroupBy, context)}),0)`;
        case 'lag':
            return `LAG(${finalExpression}) OVER (${windowClause})`;
        case 'lead':
            return `LEAD(${finalExpression}) OVER (${windowClause})`;
        // Ranking functions (these don't need the expression, just the window clause)
        case 'rank':
            return `RANK() OVER (${windowClause})`;
        case 'dense_rank':
            return `DENSE_RANK() OVER (${windowClause})`;
        case 'row_number':
            return `ROW_NUMBER() OVER (${windowClause})`;
        case 'percent_rank':
            return `PERCENT_RANK() OVER (${windowClause})`;
        case 'ntile':
            // For ntile, we'd need an additional parameter for the number of buckets
            // For now, default to 4 quartiles
            return `NTILE(4) OVER (${windowClause})`;
        default:
            throw new Error(`Unsupported window function: ${agg}`);
    }
}
/**
 * Check if window function needs nested aggregation when there's GROUP BY
 */
function needsAggregationNesting(agg) {
    // Functions that operate on values need nested aggregation with GROUP BY
    return [
        'cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin',
        'moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min',
        'delta', 'pct_change', 'lag', 'lead'
    ].includes(agg);
}
/**
 * Wrap expression with appropriate aggregation for nested window functions
 */
function wrapWithAggregation(expression, agg) {
    // For most window functions, we use SUM for the inner aggregation
    // This handles cases like SUM(SUM("field")) OVER (...)
    switch (agg) {
        case 'cumsum':
        case 'moving_sum':
            return `SUM(${expression})`;
        case 'cumavg':
        case 'moving_avg':
            return `AVG(${expression})`;
        case 'cumcount':
        case 'moving_count':
            return `COUNT(${expression})`;
        case 'cummax':
        case 'moving_max':
            return `MAX(${expression})`;
        case 'cummin':
        case 'moving_min':
            return `MIN(${expression})`;
        case 'delta':
        case 'pct_change':
        case 'lag':
        case 'lead':
            // For analytical functions, use SUM as default
            return `SUM(${expression})`;
        default:
            return `SUM(${expression})`;
    }
}
/**
 * Generate the window clause (PARTITION BY, ORDER BY, frame)
 */
function generateWindowClause(partitionBy, orderBy, frame, agg, hasGroupBy, context) {
    const parts = [];
    // PARTITION BY - use actual column names, not aliases
    if (partitionBy && partitionBy.length > 0) {
        const partitionCols = partitionBy.map(col => resolveColumnReference(col, hasGroupBy, context));
        parts.push(`PARTITION BY ${partitionCols.join(', ')}`);
    }
    // ORDER BY - use actual column names, not aliases
    if (orderBy) {
        const orderCols = Array.isArray(orderBy) ? orderBy : [orderBy];
        const resolvedOrderCols = orderCols.map(col => resolveColumnReference(col, hasGroupBy, context));
        parts.push(`ORDER BY ${resolvedOrderCols.join(', ')}`);
    }
    // Window frame
    if (frame || needsDefaultFrame(agg)) {
        const frameClause = generateFrameClause(frame, agg);
        if (frameClause) {
            parts.push(frameClause);
        }
    }
    return parts.join(' ');
}
/**
 * Generate the frame clause for window functions
 */
function generateFrameClause(frame, agg) {
    // Default frames for different function types
    if (!frame) {
        if (agg && isCumulativeFunction(agg)) {
            return 'ROWS UNBOUNDED PRECEDING';
        }
        if (agg && isMovingFunction(agg)) {
            return 'ROWS BETWEEN 6 PRECEDING AND CURRENT ROW'; // Default 7-day window
        }
        return '';
    }
    const parts = ['ROWS BETWEEN'];
    if (frame.preceding !== undefined) {
        if (frame.preceding === Infinity) {
            parts.push('UNBOUNDED PRECEDING');
        }
        else {
            parts.push(`${frame.preceding} PRECEDING`);
        }
    }
    else {
        parts.push('CURRENT ROW');
    }
    parts.push('AND');
    if (frame.following !== undefined) {
        if (frame.following === Infinity) {
            parts.push('UNBOUNDED FOLLOWING');
        }
        else {
            parts.push(`${frame.following} FOLLOWING`);
        }
    }
    else {
        parts.push('CURRENT ROW');
    }
    return parts.join(' ');
}
/**
 * Check if aggregation needs a default frame
 */
function needsDefaultFrame(agg) {
    return !!(agg && (isCumulativeFunction(agg) || isMovingFunction(agg)));
}
/**
 * Check if aggregation is a cumulative function
 */
function isCumulativeFunction(agg) {
    return ['cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin'].includes(agg);
}
/**
 * Check if aggregation is a moving function
 */
function isMovingFunction(agg) {
    return ['moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min'].includes(agg);
}
/**
 * Resolve column reference for window functions
 * This function resolves aliases back to their actual SQL expressions
 */
function resolveColumnReference(col, hasGroupBy, context) {
    // If we have context, try to resolve aliases to actual expressions
    if (context?.processedColumns) {
        const matchingColumn = context.processedColumns.find(pc => pc.alias === col);
        if (matchingColumn) {
            // For date binning columns, we need to use the actual SQL expression
            if (matchingColumn.dateBin && matchingColumn.field) {
                // Generate the same date binning expression that was used in SELECT
                return generateDateBinExpression(`"${matchingColumn.field}"`, matchingColumn.dateBin);
            }
            // For regular fields, use the field name
            if (matchingColumn.field && !matchingColumn.agg) {
                return `"${matchingColumn.field}"`;
            }
        }
    }
    // Fallback: just quote the column name
    return `"${col}"`;
}
/**
 * Generate date binning expression (simplified version for window functions)
 */
function generateDateBinExpression(fieldExpression, dateBin) {
    switch (dateBin) {
        case 'day':
            return `DATE(${fieldExpression})`;
        case 'week':
            return `DATE_TRUNC('week', ${fieldExpression})`;
        case 'month':
            return `DATE_TRUNC('month', ${fieldExpression})`;
        case 'quarter':
            return `DATE_TRUNC('quarter', ${fieldExpression})`;
        case 'year':
            return `DATE_TRUNC('year', ${fieldExpression})`;
        case 'day_of_week':
            return `EXTRACT(DOW FROM ${fieldExpression})`;
        case 'month_of_year':
            return `EXTRACT(MONTH FROM ${fieldExpression})`;
        case 'quarter_of_year':
            return `EXTRACT(QUARTER FROM ${fieldExpression})`;
        default:
            return fieldExpression;
    }
}
//# sourceMappingURL=windowFunctions.js.map