import { Column, ProcessedColumn, ProcessedTable, ProcessedJoin } from '../types';
/**
 * Process all columns and generate their SQL expressions
 */
export declare function processColumns(columns: Column[], mainTable: ProcessedTable, joins: ProcessedJoin[], allowedColumns: string[]): ProcessedColumn[];
/**
 * Determine which columns need to be in GROUP BY
 */
export declare function determineGroupByColumns(processedColumns: ProcessedColumn[]): string[];
/**
 * Check if any columns use aggregation
 */
export declare function hasAggregatedColumns(processedColumns: ProcessedColumn[]): boolean;
/**
 * Check if any columns use window functions
 */
export declare function hasWindowFunctions(processedColumns: ProcessedColumn[]): boolean;
/**
 * Generate SELECT clause SQL
 */
export declare function generateSelectSQL(processedColumns: ProcessedColumn[]): string;
//# sourceMappingURL=processor.d.ts.map