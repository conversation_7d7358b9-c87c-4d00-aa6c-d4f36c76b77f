import { TableRef, Join, ProcessedTable, ProcessedJoin } from '../types';
/**
 * Process a table reference into a standardized format
 */
export declare function processTable(table: string | TableRef): ProcessedTable;
/**
 * Process joins and generate SQL conditions
 */
export declare function processJoins(joins: Join[], mainTable: ProcessedTable, allowedColumns: string[]): ProcessedJoin[];
/**
 * Qualify a field name with table if needed
 */
export declare function qualifyFieldName(field: string, table: ProcessedTable): string;
/**
 * Extract table name from a qualified field (e.g., "table.field" -> "table")
 */
export declare function extractTableFromField(field: string): string | null;
/**
 * Extract field name from a qualified field (e.g., "table.field" -> "field")
 */
export declare function extractFieldFromQualified(field: string): string;
/**
 * Get all table names/aliases from the query
 */
export declare function getAllTableNames(mainTable: ProcessedTable, joins: ProcessedJoin[]): string[];
/**
 * Find which table a field belongs to (if specified)
 */
export declare function findTableForField(field: string, tables: ProcessedTable[]): ProcessedTable | null;
/**
 * Validate that all referenced tables exist
 */
export declare function validateTableReferences(fields: string[], mainTable: ProcessedTable, joins: ProcessedJoin[]): void;
/**
 * Generate SQL for table and joins
 */
export declare function generateTableSQL(mainTable: ProcessedTable, joins: ProcessedJoin[]): string;
/**
 * Get all columns that should be available for reference
 */
export declare function getAllowedColumns(mainTable: ProcessedTable, joins: ProcessedJoin[]): string[];
//# sourceMappingURL=tableUtils.d.ts.map