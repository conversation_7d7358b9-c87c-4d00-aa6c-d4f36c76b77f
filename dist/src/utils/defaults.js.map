{"version": 3, "file": "defaults.js", "sourceRoot": "", "sources": ["../../../src/utils/defaults.ts"], "names": [], "mappings": ";AAAA,yCAAyC;;AAOzC,kDAwCC;AAoBD,kDAMC;AAKD,oDAKC;AAKD,4CAQC;AAKD,oDAKC;AAKD,gDAEC;AAKD,kDAIC;AAKD,wCAQC;AAKD,wCAYC;AApJD;;GAEG;AACH,SAAgB,mBAAmB,CAAC,MAAc;IAChD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,wDAAwD;IACxD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,2DAA2D;QAC3D,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACzD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,8BAA8B;IAC9B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,GAAG,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;IACtC,CAAC;IAED,+BAA+B;IAC/B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,GAAG,SAAS,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC;IAED,kCAAkC;IAClC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,GAAG,SAAS,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC/C,CAAC;IAED,iBAAiB;IACjB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,OAAO,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,WAAW;IACX,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,KAAa;IACrC,kEAAkE;IAClE,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC,CAAC,CAAC,KAAK,CAAC;IAExE,qCAAqC;IACrC,OAAO,SAAS;SACb,WAAW,EAAE;SACb,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC;SAC1B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;SACnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,MAAc;IAChD,OAAO;QACL,GAAG,MAAM;QACT,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,OAAO;QAC5B,EAAE,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;KACnD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,OAAgB;IACnD,OAAO;QACL,GAAG,OAAO;QACV,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,KAAK;KACtC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,GAAoB;IACnD,MAAM,eAAe,GAAG;QACtB,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;QAClD,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY;QACtE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM;QACpC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO;KAC5D,CAAC;IACF,OAAO,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAAC,GAAoB;IACvD,MAAM,WAAW,GAAG;QAClB,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;KACpE,CAAC;IACF,OAAO,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,MAAc;IAC/C,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,OAAiB;IACnD,OAAO,OAAO;SACX,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;SACvC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,MAAc;IAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,MAAc;IAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;AACH,CAAC"}