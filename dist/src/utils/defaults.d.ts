import { Column, Filter, OrderBy, AggregationType } from '../types';
/**
 * Generate a default alias for a column based on its configuration
 */
export declare function generateColumnAlias(column: Column): string;
/**
 * Apply default values to a filter
 */
export declare function applyFilterDefaults(filter: Filter): Filter;
/**
 * Apply default values to an order by clause
 */
export declare function applyOrderByDefaults(orderBy: OrderBy): OrderBy;
/**
 * Check if an aggregation type is a window function
 */
export declare function isWindowFunction(agg: AggregationType): boolean;
/**
 * Check if an aggregation type is a regular aggregation
 */
export declare function isRegularAggregation(agg: AggregationType): boolean;
/**
 * Check if a column is aggregated (either regular agg or window function)
 */
export declare function isColumnAggregated(column: Column): boolean;
/**
 * Auto-determine groupBy columns from non-aggregated columns
 */
export declare function autoGenerateGroupBy(columns: Column[]): string[];
/**
 * Validate that required fields are present
 */
export declare function validateColumn(column: Column): void;
/**
 * Validate that a filter has the required fields
 */
export declare function validateFilter(filter: Filter): void;
//# sourceMappingURL=defaults.d.ts.map