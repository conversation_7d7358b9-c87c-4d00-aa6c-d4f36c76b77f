{"version": 3, "file": "tableUtils.js", "sourceRoot": "", "sources": ["../../../src/utils/tableUtils.ts"], "names": [], "mappings": ";AAAA,2BAA2B;;AAQ3B,oCAaC;AAKD,oCAyBC;AAKD,4CAQC;AAKD,sDAGC;AAKD,8DAGC;AAKD,4CAMC;AAKD,8CASC;AAKD,0DAcC;AAKD,4CAeC;AAKD,8CAQC;AAxJD;;GAEG;AACH,SAAgB,YAAY,CAAC,KAAwB;IACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO;YACL,IAAI,EAAE,KAAK;YACX,aAAa,EAAE,KAAK;SACrB,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,aAAa,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI;KACzC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,KAAa,EAAE,SAAyB,EAAE,cAAwB;IAC7F,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACtB,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEhD,IAAI,YAAoB,CAAC;QAEzB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,sEAAsE;YACtE,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,CAAC;aAAM,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACnB,gDAAgD;YAChD,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC5D,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;YACnE,YAAY,GAAG,GAAG,SAAS,MAAM,UAAU,EAAE,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,OAAO;YACL,GAAG,IAAI;YACP,KAAK,EAAE,cAAc;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO;YAC1B,YAAY;SACb,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAa,EAAE,KAAqB;IACnE,iEAAiE;IACjE,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2CAA2C;IAC3C,OAAO,GAAG,KAAK,CAAC,aAAa,IAAI,KAAK,EAAE,CAAC;AAC3C,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,KAAa;IACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CAAC,KAAa;IACrD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,SAAyB,EAAE,KAAsB;IAChF,MAAM,KAAK,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IACxC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,KAAa,EAAE,MAAwB;IACvE,MAAM,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC/C,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACzB,KAAK,CAAC,aAAa,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAC9D,IAAI,IAAI,CAAC;AACZ,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CACrC,MAAgB,EAChB,SAAyB,EACzB,KAAsB;IAEtB,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1D,MAAM,UAAU,GAAG,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAEtD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,SAAS,2CAA2C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpH,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,SAAyB,EAAE,KAAsB;IAChF,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK;QACvB,CAAC,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,KAAK,GAAG;QAC/C,CAAC,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC;IAE1B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAK,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;YAC/B,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACjD,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;QAE3B,GAAG,IAAI,IAAI,QAAQ,SAAS,QAAQ,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IACjE,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,SAAyB,EAAE,KAAsB;IACjF,yEAAyE;IACzE,4DAA4D;IAC5D,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAEvD,gEAAgE;IAChE,+CAA+C;IAC/C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAC1C,CAAC"}