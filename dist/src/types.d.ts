export interface QueryConfig {
    table: string | TableRef;
    joins?: Join[];
    columns: Column[];
    filters?: Filter[];
    groupBy?: string[];
    orderBy?: OrderBy[];
    limit?: number;
    offset?: number;
}
export interface TableRef {
    name: string;
    alias?: string;
}
export interface Join {
    table: string | TableRef;
    type?: JoinType;
    on?: {
        left: string;
        right: string;
    };
    condition?: string;
}
export interface Column {
    alias?: string;
    field?: string;
    table?: string;
    expr?: string;
    agg?: AggregationType;
    partitionBy?: string[];
    orderBy?: string | string[];
    frame?: WindowFrame;
    dateBin?: DateBin;
    binCount?: number;
}
export interface Filter {
    field?: string;
    table?: string;
    op?: FilterOperator;
    value?: any;
    expr?: string;
    type?: FilterType;
}
export interface OrderBy {
    column: string;
    direction?: 'asc' | 'desc';
}
export interface WindowFrame {
    preceding?: number;
    following?: number;
}
export type JoinType = 'inner' | 'left' | 'right' | 'full';
export type AggregationType = 'sum' | 'avg' | 'count' | 'max' | 'min' | 'median' | 'stddev' | 'variance' | 'cumsum' | 'cumavg' | 'cumcount' | 'cummax' | 'cummin' | 'moving_avg' | 'moving_sum' | 'moving_count' | 'moving_max' | 'moving_min' | 'delta' | 'pct_change' | 'lag' | 'lead' | 'rank' | 'dense_rank' | 'row_number' | 'percent_rank' | 'ntile';
export type FilterOperator = 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like' | 'ilike';
export type FilterType = 'where' | 'having' | 'qualify';
export type DateBin = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'day_of_week' | 'month_of_year' | 'quarter_of_year';
export interface ProcessedColumn extends Column {
    alias: string;
    isAggregated: boolean;
    isWindowFunction: boolean;
    sqlExpression: string;
}
export interface ProcessedTable {
    name: string;
    alias?: string;
    qualifiedName: string;
}
export interface ProcessedJoin extends Join {
    table: ProcessedTable;
    sqlCondition: string;
}
export interface ExpressionContext {
    allowedColumns: string[];
    tables: ProcessedTable[];
    hasGroupBy: boolean;
    processedColumns: ProcessedColumn[];
}
//# sourceMappingURL=types.d.ts.map