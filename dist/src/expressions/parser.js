"use strict";
// Metabase-compatible expression parser and SQL generator
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseExpression = parseExpression;
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
// Parse and convert Metabase expressions to SQL
function parseExpression(expr, context) {
    // Remove extra whitespace and normalize
    let normalized = expr.trim();
    // First, replace all [column] references with proper SQL column references
    const columnReferenceRegex = /\[([^\]]+)\]/g;
    normalized = normalized.replace(columnReferenceRegex, (_, columnName) => {
        return knex.ref(columnName).toSQL().sql;
    });
    // Then, replace all function calls in the expression
    const functionRegex = /(\w+)\s*\(([^)]*)\)/g;
    normalized = normalized.replace(functionRegex, (_, funcName, args) => {
        return convertFunctionToSQL(funcName, args, context);
    });
    // Handle single-quoted string literals (these are definitely string values)
    if (normalized.match(/^'[^']*'$/)) {
        return normalized; // Keep single quotes as-is
    }
    // If it's already a quoted SQL identifier (like "Sublayer"), return as-is
    if (normalized.match(/^"[^"]*"$/) && !normalized.includes(' ')) {
        return normalized; // Keep as quoted identifier
    }
    // Handle numeric literals
    if (normalized.match(/^-?\d+(\.\d+)?$/)) {
        return normalized;
    }
    // Return as-is for complex expressions (fallback)
    return normalized;
}
// Convert Metabase function calls to SQL
function convertFunctionToSQL(funcName, args, context) {
    const parsedArgs = parseArguments(args);
    switch (funcName.toLowerCase()) {
        // AGGREGATION FUNCTIONS
        case 'average':
            return `AVG(${parseExpression(parsedArgs[0], context)})`;
        case 'count':
            return parsedArgs.length > 0 ? `COUNT(${parseExpression(parsedArgs[0], context)})` : 'COUNT(*)';
        case 'countif':
            return `COUNT(CASE WHEN ${parseExpression(parsedArgs[0], context)} THEN 1 END)`;
        case 'distinct':
            return `COUNT(DISTINCT ${parseExpression(parsedArgs[0], context)})`;
        case 'distinctif':
            return `COUNT(DISTINCT CASE WHEN ${parseExpression(parsedArgs[1], context)} THEN ${parseExpression(parsedArgs[0], context)} END)`;
        case 'max':
            return `MAX(${parseExpression(parsedArgs[0], context)})`;
        case 'median':
            return `PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ${parseExpression(parsedArgs[0], context)})`;
        case 'min':
            return `MIN(${parseExpression(parsedArgs[0], context)})`;
        case 'share':
            return `AVG(CASE WHEN ${parseExpression(parsedArgs[0], context)} THEN 1.0 ELSE 0.0 END)`;
        case 'standarddeviation':
            return `STDDEV(${parseExpression(parsedArgs[0], context)})`;
        case 'sum':
            return `SUM(${parseExpression(parsedArgs[0], context)})`;
        case 'sumif':
            return `SUM(CASE WHEN ${parseExpression(parsedArgs[1], context)} THEN ${parseExpression(parsedArgs[0], context)} ELSE 0 END)`;
        case 'variance':
            return `VARIANCE(${parseExpression(parsedArgs[0], context)})`;
        // LOGICAL FUNCTIONS
        case 'and':
            return `(${parsedArgs.map(arg => parseExpression(arg, context)).join(' AND ')})`;
        case 'or':
            return `(${parsedArgs.map(arg => parseExpression(arg, context)).join(' OR ')})`;
        case 'not':
            return `NOT (${parseExpression(parsedArgs[0], context)})`;
        case 'case':
        case 'if':
            return generateCaseExpression(parsedArgs, context);
        case 'between':
            return `${parseExpression(parsedArgs[0], context)} BETWEEN ${parseExpression(parsedArgs[1], context)} AND ${parseExpression(parsedArgs[2], context)}`;
        case 'in':
            return `${parseExpression(parsedArgs[0], context)} IN (${parsedArgs.slice(1).map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'notin':
            return `${parseExpression(parsedArgs[0], context)} NOT IN (${parsedArgs.slice(1).map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'notnull':
            return `${parseExpression(parsedArgs[0], context)} IS NOT NULL`;
        // MATH FUNCTIONS
        case 'abs':
            return `ABS(${parseExpression(parsedArgs[0], context)})`;
        case 'ceil':
            return `CEIL(${parseExpression(parsedArgs[0], context)})`;
        case 'exp':
            return `EXP(${parseExpression(parsedArgs[0], context)})`;
        case 'floor':
            return `FLOOR(${parseExpression(parsedArgs[0], context)})`;
        case 'log':
            return `LN(${parseExpression(parsedArgs[0], context)})`;
        case 'power':
            return `POWER(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)})`;
        case 'round':
            return parsedArgs.length > 1
                ? `ROUND(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)})`
                : `ROUND(${parseExpression(parsedArgs[0], context)})`;
        case 'sqrt':
            return `SQRT(${parseExpression(parsedArgs[0], context)})`;
        // STRING FUNCTIONS
        case 'concat':
            return `CONCAT(${parsedArgs.map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'contains':
            return `${parseExpression(parsedArgs[0], context)} LIKE '%' || ${parseExpression(parsedArgs[1], context)} || '%'`;
        case 'doesnotcontain':
            return `${parseExpression(parsedArgs[0], context)} NOT LIKE '%' || ${parseExpression(parsedArgs[1], context)} || '%'`;
        case 'endswith':
            return `${parseExpression(parsedArgs[0], context)} LIKE '%' || ${parseExpression(parsedArgs[1], context)}`;
        case 'startswith':
            return `${parseExpression(parsedArgs[0], context)} LIKE ${parseExpression(parsedArgs[1], context)} || '%'`;
        case 'length':
            return `LENGTH(${parseExpression(parsedArgs[0], context)})`;
        case 'lower':
            return `LOWER(${parseExpression(parsedArgs[0], context)})`;
        case 'upper':
            return `UPPER(${parseExpression(parsedArgs[0], context)})`;
        case 'trim':
            return `TRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'ltrim':
            return `LTRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'rtrim':
            return `RTRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'substring':
            return parsedArgs.length > 2
                ? `SUBSTRING(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)}, ${parseExpression(parsedArgs[2], context)})`
                : `SUBSTRING(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)})`;
        case 'replace':
            return `REPLACE(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)}, ${parseExpression(parsedArgs[2], context)})`;
        case 'isempty':
            return `(${parseExpression(parsedArgs[0], context)} IS NULL OR ${parseExpression(parsedArgs[0], context)} = '')`;
        case 'notempty':
            return `(${parseExpression(parsedArgs[0], context)} IS NOT NULL AND ${parseExpression(parsedArgs[0], context)} != '')`;
        // DATE FUNCTIONS
        case 'year':
            return `EXTRACT(YEAR FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'month':
            return `EXTRACT(MONTH FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'day':
            return `EXTRACT(DAY FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'hour':
            return `EXTRACT(HOUR FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'minute':
            return `EXTRACT(MINUTE FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'second':
            return `EXTRACT(SECOND FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'weekday':
            return `EXTRACT(DOW FROM ${parseExpression(parsedArgs[0], context)}) + 1`;
        case 'quarter':
            return `EXTRACT(QUARTER FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'date':
            return `DATE(${parseExpression(parsedArgs[0], context)})`;
        case 'datetime':
            return `${parseExpression(parsedArgs[0], context)}::timestamp`;
        case 'now':
            return 'NOW()';
        // TYPE CASTING FUNCTIONS
        case 'text':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS TEXT)`;
        case 'integer':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS INTEGER)`;
        case 'float':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS FLOAT)`;
        // LOGICAL FUNCTIONS
        case 'coalesce':
            return `COALESCE(${parsedArgs.map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'isnull':
            return `${parseExpression(parsedArgs[0], context)} IS NULL`;
        default:
            // For unknown functions, pass through as-is (might be PostgreSQL functions)
            return `${funcName}(${args})`;
    }
}
// Generate CASE expression
function generateCaseExpression(args, context) {
    if (args.length < 3) {
        throw new Error('CASE expression requires at least 3 arguments');
    }
    let sql = 'CASE';
    // Process condition-value pairs
    for (let i = 0; i < args.length - 1; i += 2) {
        if (i + 1 < args.length - 1) {
            sql += ` WHEN ${parseExpression(args[i], context)} THEN ${parseExpression(args[i + 1], context)}`;
        }
    }
    // Add ELSE clause (last argument)
    sql += ` ELSE ${parseExpression(args[args.length - 1], context)} END`;
    return sql;
}
// Parse function arguments, handling nested parentheses and quotes
function parseArguments(args) {
    if (!args.trim())
        return [];
    const result = [];
    let current = '';
    let depth = 0;
    let inQuotes = false;
    let quoteChar = '';
    for (let i = 0; i < args.length; i++) {
        const char = args[i];
        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
            current += char;
        }
        else if (inQuotes && char === quoteChar) {
            inQuotes = false;
            current += char;
        }
        else if (!inQuotes && char === '(') {
            depth++;
            current += char;
        }
        else if (!inQuotes && char === ')') {
            depth--;
            current += char;
        }
        else if (!inQuotes && char === ',' && depth === 0) {
            result.push(current.trim());
            current = '';
        }
        else {
            current += char;
        }
    }
    if (current.trim()) {
        result.push(current.trim());
    }
    return result;
}
//# sourceMappingURL=parser.js.map