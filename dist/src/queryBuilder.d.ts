import knexFactory from 'knex';
export type AggFn = 'sum' | 'avg' | 'min' | 'max' | 'count' | 'countDistinct';
export type WindowFn = 'cumsum' | 'cumcount' | 'moving_avg' | 'moving_sum' | 'median' | 'percentile' | 'rank' | 'dense_rank' | 'row_number' | 'delta' | 'pct_change';
export type DateBin = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'day_of_week' | 'day_of_month' | 'day_of_year' | 'week_of_year' | 'month_of_year' | 'quarter_of_year' | 'year_of_era' | 'dont_bin';
export interface Dimension {
    field: string;
    dateBin?: DateBin;
    alias?: string;
}
export interface Measure {
    field?: string;
    agg?: AggFn;
    fn?: WindowFn;
    orderBy?: string;
    partitionBy?: string[];
    frame?: {
        preceding: number | null;
        following: number | null;
    };
    percentile?: number;
    expr?: string;
    alias?: string;
}
export interface CustomColumn {
    alias: string;
    expr: string;
}
export interface Filter {
    field: string;
    op: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like';
    value: any | any[];
    having?: boolean;
}
export interface ChartConfig {
    table: string;
    dimensions?: (string | Dimension)[];
    customColumns?: CustomColumn[];
    measures?: Measure[];
    filters?: Filter[];
    orderBy?: {
        field: string;
        direction: 'asc' | 'desc';
    }[];
    limit?: number;
    offset?: number;
}
export declare function buildQuery(cfg: ChartConfig): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: false;
    _keys: never;
    _aliases: {};
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
//# sourceMappingURL=queryBuilder.d.ts.map