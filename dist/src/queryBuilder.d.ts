import knexFactory from 'knex';
export type AggFn = 'sum' | 'avg' | 'min' | 'max' | 'count' | 'countDistinct';
export type WindowFn = 'cumsum' | 'cumcount' | 'moving_avg' | 'moving_sum' | 'median' | 'percentile' | 'rank' | 'dense_rank' | 'row_number' | 'delta' | 'pct_change';
export type DateBin = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'day_of_week' | 'day_of_month' | 'day_of_year' | 'week_of_year' | 'month_of_year' | 'quarter_of_year' | 'year_of_era' | 'dont_bin';
export interface Dimension {
    field: string;
    dateBin?: DateBin;
    binCount?: number;
    alias?: string;
}
export interface Measure {
    field?: string;
    agg?: AggFn;
    fn?: WindowFn;
    orderBy?: string;
    partitionBy?: string[];
    frame?: {
        preceding: number | null;
        following: number | null;
    };
    percentile?: number;
    expr?: string;
    alias?: string;
}
export interface CustomColumn {
    alias: string;
    expr: string;
}
export type MetabaseExpressionFunction = 'Average' | 'Count' | 'CountIf' | 'Distinct' | 'DistinctIf' | 'Max' | 'Median' | 'Min' | 'Percentile' | 'Share' | 'StandardDeviation' | 'Sum' | 'SumIf' | 'Variance' | 'CumulativeSum' | 'CumulativeCount' | 'between' | 'case' | 'coalesce' | 'if' | 'in' | 'isNull' | 'notIn' | 'notNull' | 'abs' | 'ceil' | 'exp' | 'floor' | 'integer' | 'log' | 'power' | 'round' | 'sqrt' | 'concat' | 'contains' | 'date' | 'doesNotContain' | 'domain' | 'endsWith' | 'float' | 'host' | 'isEmpty' | 'lTrim' | 'length' | 'lower' | 'notEmpty' | 'path' | 'regexExtract' | 'replace' | 'splitPart' | 'rTrim' | 'startsWith' | 'subdomain' | 'substring' | 'text' | 'trim' | 'upper' | 'convertTimezone' | 'datetime' | 'datetimeAdd' | 'datetimeDiff' | 'datetimeSubtract' | 'day' | 'dayName' | 'hour' | 'interval' | 'minute' | 'month' | 'monthName' | 'now' | 'quarter' | 'quarterName' | 'relativeDateTime' | 'second' | 'timeSpan' | 'week' | 'weekday' | 'year' | 'Offset';
export interface Filter {
    field?: string;
    op?: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like';
    value?: any | any[];
    having?: boolean;
    expr?: string;
}
export interface ChartConfig {
    table: string;
    dimensions?: (string | Dimension)[];
    customColumns?: CustomColumn[];
    measures?: Measure[];
    filters?: Filter[];
    orderBy?: {
        field: string;
        direction: 'asc' | 'desc';
    }[];
    limit?: number;
    offset?: number;
}
export declare function buildQuery(cfg: ChartConfig): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: false;
    _keys: never;
    _aliases: {};
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
export declare function cleanup(): Promise<void>;
//# sourceMappingURL=queryBuilder.d.ts.map