import knexFactory from 'knex';
import { QueryConfig } from './types';
/**
 * Build a SQL query from the unified configuration
 */
export declare function buildQuery(config: QueryConfig): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: true;
    _keys: never;
    _aliases: knexFactory.Knex.Raw<any>;
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
/**
 * Legacy function for backward compatibility with old ChartConfig format
 */
export declare function buildQueryLegacy(config: any): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: true;
    _keys: never;
    _aliases: knexFactory.Knex.Raw<any>;
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
/**
 * Cleanup function for database connections
 */
export declare function cleanup(): Promise<void>;
export * from './types';
export interface ChartConfig {
    table: string;
    dimensions?: (string | {
        field: string;
        dateBin?: string;
        binCount?: number;
        alias?: string;
    })[];
    measures?: {
        field?: string;
        agg?: string;
        fn?: string;
        alias?: string;
        partitionBy?: string[];
        orderBy?: string;
        frame?: any;
    }[];
    customColumns?: {
        expr: string;
        alias: string;
    }[];
    filters?: {
        field?: string;
        op?: string;
        value?: any;
        having?: boolean;
        expr?: string;
    }[];
    groupBy?: string[];
    orderBy?: {
        column: string;
        direction?: 'asc' | 'desc';
    }[];
    limit?: number;
    offset?: number;
}
export declare function buildQueryAuto(config: QueryConfig | ChartConfig): any;
//# sourceMappingURL=queryBuilder.d.ts.map