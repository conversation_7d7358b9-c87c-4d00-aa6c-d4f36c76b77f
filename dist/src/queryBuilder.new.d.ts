import knexFactory from 'knex';
import { QueryConfig } from './types';
/**
 * Build a SQL query from the unified configuration
 */
export declare function buildQuery(config: QueryConfig): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: true;
    _keys: never;
    _aliases: knexFactory.Knex.Raw<any>;
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
/**
 * Legacy function for backward compatibility
 */
export declare function buildQueryLegacy(config: any): knexFactory.Knex.QueryBuilder<any, {
    _base: any;
    _hasSelection: true;
    _keys: never;
    _aliases: knexFactory.Knex.Raw<any>;
    _single: false;
    _intersectProps: {};
    _unionProps: never;
}[]>;
/**
 * Cleanup function for database connections
 */
export declare function cleanup(): Promise<void>;
export * from './types';
//# sourceMappingURL=queryBuilder.new.d.ts.map