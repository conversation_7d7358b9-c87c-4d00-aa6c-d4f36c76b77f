{"version": 3, "file": "queryBuilder.js", "sourceRoot": "", "sources": ["../../src/queryBuilder.ts"], "names": [], "mappings": ";AAAA,2CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe3C,gCAgEC;AAKD,4CA+DC;AAKD,0BAEC;AAmBD,wCAQC;AAnLD,gDAA+B;AAC/B,iDAAmC;AAEnC,+CAA6E;AAC7E,mDAAqG;AACrG,mDAA2I;AAC3I,mDAAmD;AAEnD,MAAM,IAAI,GAAG,IAAA,cAAW,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AAE3E;;GAEG;AACH,SAAgB,UAAU,CAAC,MAAmB;IAC1C,+BAA+B;IAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACzC,CAAC;IACD,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvD,CAAC;IAED,2BAA2B;IAC3B,MAAM,SAAS,GAAG,IAAA,yBAAY,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAG,IAAA,yBAAY,EAAC,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IAC9D,MAAM,cAAc,GAAG,IAAA,8BAAiB,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAE3D,+DAA+D;IAC/D,MAAM,oBAAoB,GAAG,IAAA,0BAAc,EAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;IACrG,MAAM,YAAY,GAAG,IAAA,gCAAoB,EAAC,oBAAoB,CAAC,CAAC;IAEhE,8EAA8E;IAC9E,MAAM,gBAAgB,GAAG,IAAA,0BAAc,EAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;IAExG,MAAM,cAAc,GAAG,YAAY;QAC/B,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,IAAA,mCAAuB,EAAC,gBAAgB,CAAC,CAAC;QAC/D,CAAC,CAAC,EAAE,CAAC;IAET,2BAA2B;IAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAA,6BAAiB,EAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEvE,6BAA6B;IAC7B,MAAM,QAAQ,GAAG,IAAA,6BAAgB,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACpD,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEvC,gBAAgB;IAChB,IAAA,wBAAY,EACR,KAAK,EACL,MAAM,CAAC,OAAO,IAAI,EAAE,EACpB,SAAS,EACT,KAAK,EACL,cAAc,EACd,YAAY,CACf,CAAC;IAEF,yBAAyB;IACzB,IAAI,YAAY,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5C,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,eAAe;IACf,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,gBAAgB,GAAG,IAAA,+BAAoB,EAAC,OAAO,CAAC,CAAC;YACvD,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACP,CAAC;IAED,uBAAuB;IACvB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAChB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,MAAW;IACxC,mCAAmC;IACnC,MAAM,SAAS,GAAgB;QAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,MAAM,EAAE,MAAM,CAAC,MAAM;KACxB,CAAC;IAEF,gCAAgC;IAChC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;QACpB,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;YACnC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC1B,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,KAAK,EAAE,GAAG,CAAC,KAAK;iBACnB,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAA8B;IAC9B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE;YACrC,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;gBACb,kBAAkB;gBAClB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,GAAG,EAAE,OAAO,CAAC,EAAE;oBACf,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;iBACvB,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,sBAAsB;gBACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,KAAK,EAAE,OAAO,CAAC,KAAK;iBACvB,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oCAAoC;IACpC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;YACtC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,GAAG,CAAC,KAAK;aACnB,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,OAAO;IACzB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACzB,CAAC;AAED,gCAAgC;AAChC,0CAAwB;AAexB,uDAAuD;AACvD,SAAgB,cAAc,CAAC,MAAiC;IAC5D,wDAAwD;IACxD,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;QACtB,OAAO,UAAU,CAAC,MAAqB,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACJ,gBAAgB;QAChB,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;AACL,CAAC"}