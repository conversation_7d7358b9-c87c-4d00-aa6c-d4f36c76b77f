"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildQuery = buildQuery;
exports.cleanup = cleanup;
// queryBuilder.ts
const knex_1 = __importDefault(require("knex"));
const pgsql_ast_parser_1 = require("pgsql-ast-parser");
const process = __importStar(require("process"));
const expressions_1 = require("./expressions");
// 1‑A  Knex init (pool, SSL, etc.)
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
// 1‑B  Allowed tables/columns (whitelist)
const ALLOW_LIST = {
    orders: ['id', 'region', 'sale_date', 'quantity', 'unit_price', 'amount'],
    customers: ['id', 'name', 'signup_channel'],
    progress_history: ['Activity', 'Subactivity', 'Layer', 'Sublayer', 'Date', 'Scope', 'Work done']
};
const SAFE_FUNCS = new Set([
    'sum', 'avg', 'min', 'max', 'count', 'count_distinct',
    'rank', 'dense_rank', 'row_number',
    'percentile_cont', 'percentile_disc',
    'lag', 'lead', 'sum', 'avg'
]);
function validateExpr(expr, allowedCols) {
    // Skip validation for Metabase-style expressions (contains brackets or function calls)
    if (expr.includes('[') || (expr.includes('(') && !expr.startsWith('('))) {
        // For Metabase expressions, do basic validation by extracting column references
        const columnMatches = expr.match(/\[([^\]]+)\]/g);
        if (columnMatches) {
            for (const match of columnMatches) {
                const columnName = match.slice(1, -1); // Remove brackets
                if (!allowedCols.includes(columnName)) {
                    throw new Error(`Illegal column ${columnName}`);
                }
            }
        }
        return; // Skip PostgreSQL AST validation for Metabase expressions
    }
    const ast = (0, pgsql_ast_parser_1.parse)(`SELECT ${expr}`)[0]; // Wrap to parse  :contentReference[oaicite:4]{index=4}
    const v = (0, pgsql_ast_parser_1.astVisitor)(() => ({
        ref(col) {
            if (!allowedCols.includes(col.name)) {
                throw new Error(`Illegal column ${col.name}`);
            }
        },
        funcCall(fn) {
            const name = fn.function.name.toLowerCase();
            if (!SAFE_FUNCS.has(name))
                throw new Error(`Func ${name} not allowed`);
        },
        select() { }, windowDef() { }, aConst() { }, // ok
        insert() { throw 'Insert forbidden'; },
        update() { throw 'Update forbidden'; },
        delete() { throw 'Delete forbidden'; },
        selectStmt(inner) { if (inner !== ast)
            throw 'Sub‑selects forbidden'; }
    }));
    v.statement(ast); // Walk AST – errors throw
}
// Helper function to generate date binning SQL expressions
function getDateBinExpression(field, dateBin) {
    const col = knex.ref(field).toSQL().sql;
    switch (dateBin) {
        case 'day':
            return `DATE(${col})`;
        case 'week':
            return `DATE_TRUNC('week', ${col})`;
        case 'month':
            return `DATE_TRUNC('month', ${col})`;
        case 'quarter':
            return `DATE_TRUNC('quarter', ${col})`;
        case 'year':
            return `DATE_TRUNC('year', ${col})`;
        case 'day_of_week':
            return `EXTRACT(DOW FROM ${col})`; // 0=Sunday, 6=Saturday
        case 'day_of_month':
            return `EXTRACT(DAY FROM ${col})`;
        case 'day_of_year':
            return `EXTRACT(DOY FROM ${col})`;
        case 'week_of_year':
            return `EXTRACT(WEEK FROM ${col})`;
        case 'month_of_year':
            return `EXTRACT(MONTH FROM ${col})`;
        case 'quarter_of_year':
            return `EXTRACT(QUARTER FROM ${col})`;
        case 'year_of_era':
            return `EXTRACT(YEAR FROM ${col})`;
        case 'dont_bin':
        default:
            return col;
    }
}
// Helper function to generate numeric binning SQL expressions
function getNumericBinExpression(field, binCount) {
    const col = knex.ref(field).toSQL().sql;
    // Use WIDTH_BUCKET for PostgreSQL - it automatically calculates bin ranges
    // WIDTH_BUCKET(value, min_value, max_value, num_buckets)
    // We'll use a subquery to get min/max values dynamically
    return `WIDTH_BUCKET(${col},
        (SELECT MIN(${col}) FROM (SELECT ${col} FROM ${knex.ref('__table__').toSQL().sql}) t),
        (SELECT MAX(${col}) FROM (SELECT ${col} FROM ${knex.ref('__table__').toSQL().sql}) t),
        ${binCount})`;
}
// Helper function to normalize dimension to get field name and alias
function normalizeDimension(dim, tableName) {
    if (typeof dim === 'string') {
        return { field: dim, alias: dim, expression: knex.ref(dim).toSQL().sql };
    }
    const field = dim.field;
    // Determine alias based on binning type
    let alias = dim.alias;
    if (!alias) {
        if (dim.dateBin && dim.dateBin !== 'dont_bin') {
            alias = `${field}_${dim.dateBin}`;
        }
        else if (dim.binCount) {
            alias = `${field}_bin_${dim.binCount}`;
        }
        else {
            alias = field;
        }
    }
    // Determine expression based on binning type
    let expression;
    if (dim.dateBin && dim.dateBin !== 'dont_bin') {
        expression = getDateBinExpression(field, dim.dateBin);
    }
    else if (dim.binCount) {
        // For numeric binning, we need to replace __table__ placeholder with actual table name
        expression = getNumericBinExpression(field, dim.binCount).replace(new RegExp(knex.ref('__table__').toSQL().sql, 'g'), knex.ref(tableName).toSQL().sql);
    }
    else {
        expression = knex.ref(field).toSQL().sql;
    }
    return { field, alias, expression };
}
function buildQuery(cfg) {
    const allowed = ALLOW_LIST[cfg.table];
    if (!allowed)
        throw new Error('Table not allowed');
    const q = knex(cfg.table); // <table>
    /* 3‑A  Dimensions */
    const processedDimensions = [];
    (cfg.dimensions ?? []).forEach(dim => {
        const normalized = normalizeDimension(dim, cfg.table);
        if (!allowed.includes(normalized.field))
            throw new Error(`Dim ${normalized.field} not allowed`);
        if (normalized.expression === normalized.alias) {
            // Simple column reference
            q.select(normalized.alias);
        }
        else {
            // Date binning expression
            q.select(knex.raw(`${normalized.expression} AS ??`, [normalized.alias]));
        }
        processedDimensions.push(normalized);
    });
    /* 3‑B  Custom (non‑agg) columns */
    (cfg.customColumns ?? []).forEach(c => {
        validateExpr(c.expr, allowed);
        // Create expression context for parsing
        const expressionContext = {
            allowedColumns: allowed,
            hasGroupBy: false, // Custom columns are not aggregated
            processedDimensions
        };
        // Parse Metabase-style expressions or use raw SQL
        const sqlExpression = c.expr.includes('[') || (c.expr.includes('(') && !c.expr.startsWith('('))
            ? (0, expressions_1.parseExpression)(c.expr, expressionContext)
            : c.expr;
        q.select(knex.raw(`${sqlExpression} AS ??`, [c.alias]));
    });
    /* 3‑C  Measures */
    const groupables = [
        ...processedDimensions.map(d => d.alias),
        ...(cfg.customColumns ?? []).map(c => c.alias)
    ];
    const hasGroupBy = !!(groupables.length && cfg.measures?.length);
    (cfg.measures ?? []).forEach(m => addMeasure(q, m, allowed, hasGroupBy, processedDimensions));
    /* 3‑D  Filters — WHERE vs HAVING */
    (cfg.filters ?? []).filter(f => !f.having).forEach(f => applyFilter(q, f, allowed, false, processedDimensions));
    (cfg.filters ?? []).filter(f => f.having).forEach(f => applyFilter(q, f, allowed, true, processedDimensions));
    /* 3‑E  GROUP BY if any measures or HAVING */
    if (hasGroupBy)
        q.groupBy(groupables);
    /* 3‑F  ORDER / PAGINATION */
    (cfg.orderBy ?? []).forEach(o => q.orderBy(o.field, o.direction));
    if (cfg.limit)
        q.limit(Math.min(cfg.limit, 10000));
    if (cfg.offset)
        q.offset(cfg.offset);
    return q;
}
// Function to close database connections and allow process to exit
async function cleanup() {
    await knex.destroy();
}
function addMeasure(q, m, allowed, hasGroupBy = false, processedDimensions = []) {
    const alias = m.alias ??
        (m.fn ? `${m.fn}_${m.field || 'expr'}` : `${m.agg}_${m.field}`);
    if (m.expr) { // raw expression
        validateExpr(m.expr, allowed);
        q.select(knex.raw(`${m.expr} AS ??`, [alias]));
        return;
    }
    if (m.fn)
        return addWindowFn(q, m, alias, allowed, hasGroupBy, processedDimensions); // window/analytic
    // simple aggregate
    if (!m.field || !allowed.includes(m.field))
        throw 'Bad measure field';
    switch (m.agg) {
        case 'sum':
            q.sum({ [alias]: m.field });
            break;
        case 'avg':
            q.avg({ [alias]: m.field });
            break;
        case 'min':
            q.min({ [alias]: m.field });
            break;
        case 'max':
            q.max({ [alias]: m.field });
            break;
        case 'count':
            q.count({ [alias]: m.field });
            break;
        case 'countDistinct':
            q.countDistinct({ [alias]: m.field });
            break;
        default: throw 'Unknown agg';
    }
}
function addWindowFn(q, m, alias, allowed, hasGroupBy = false, processedDimensions = []) {
    const col = knex.ref(m.field).toSQL().sql; // safe quote
    // Resolve ORDER BY expression - check if it's a processed dimension
    const orderByField = m.orderBy || m.field;
    const processedDim = processedDimensions.find(d => d.alias === orderByField);
    const order = processedDim ? processedDim.expression : knex.ref(orderByField).toSQL().sql;
    // Resolve PARTITION BY expressions - check if any are processed dimensions
    const partition = m.partitionBy?.map(p => {
        const partitionDim = processedDimensions.find(d => d.alias === p);
        return partitionDim ? partitionDim.expression : knex.ref(p).toSQL().sql;
    }).join(', ');
    // Helper function to build OVER clause with optional partition
    const buildOverClause = (orderBy, partitionBy) => {
        const partitionClause = partitionBy ? `PARTITION BY ${partitionBy} ` : '';
        return `${partitionClause}ORDER BY ${orderBy}`;
    };
    switch (m.fn) {
        case 'cumsum': // running total
            const cumsumExpr = hasGroupBy ? `sum(sum(${col}))` : `sum(${col})`;
            const cumsumOver = buildOverClause(order, partition);
            q.select(knex.raw(`${cumsumExpr} OVER (${cumsumOver} ROWS UNBOUNDED PRECEDING) AS ??`, [alias]));
            break;
        case 'cumcount':
            // cumcount doesn't need nested aggregation - it's always count(*)
            const cumcountOver = buildOverClause(order, partition);
            q.select(knex.raw(`count(*) OVER (${cumcountOver}) AS ??`, [alias]));
            break;
        case 'moving_avg': {
            const rows = m.frame?.preceding ?? 2;
            const avgExpr = hasGroupBy ? `avg(sum(${col}))` : `avg(${col})`;
            const avgOver = buildOverClause(order, partition);
            q.select(knex.raw(`${avgExpr} OVER (${avgOver} ROWS BETWEEN ? PRECEDING AND CURRENT ROW) AS ??`, [rows, alias]));
            break;
        }
        case 'moving_sum': {
            const rows = m.frame?.preceding ?? 2;
            const sumExpr = hasGroupBy ? `sum(sum(${col}))` : `sum(${col})`;
            const sumOver = buildOverClause(order, partition);
            q.select(knex.raw(`${sumExpr} OVER (${sumOver} ROWS BETWEEN ? PRECEDING AND CURRENT ROW) AS ??`, [rows, alias]));
            break;
        }
        case 'median':
            const medianExpr = hasGroupBy ? `sum(${col})` : col;
            q.select(knex.raw(`percentile_cont(0.5) WITHIN GROUP (ORDER BY ${medianExpr}) AS ??`, [alias]));
            break;
        case 'percentile':
            const percentileExpr = hasGroupBy ? `sum(${col})` : col;
            q.select(knex.raw(`percentile_cont(?) WITHIN GROUP (ORDER BY ${percentileExpr}) AS ??`, [m.percentile ?? 0.9, alias]));
            break;
        case 'delta':
            const deltaExpr = hasGroupBy ? `sum(${col}) - lag(sum(${col}))` : `${col} - lag(${col})`;
            const deltaOver = buildOverClause(order, partition);
            q.select(knex.raw(`${deltaExpr} OVER (${deltaOver}) AS ??`, [alias]));
            break;
        case 'pct_change':
            const pctOver = buildOverClause(order, partition);
            const pctExpr = hasGroupBy
                ? `100.0 * (sum(${col}) - lag(sum(${col})) OVER (${pctOver})) / NULLIF(lag(sum(${col})) OVER (${pctOver}),0)`
                : `100.0 * (${col} - lag(${col}) OVER (${pctOver})) / NULLIF(lag(${col}) OVER (${pctOver}),0)`;
            q.select(knex.raw(`${pctExpr} AS ??`, [alias]));
            break;
        case 'rank':
            const rankCol = hasGroupBy ? `sum(${col})` : col;
            const rankOver = partition ? `PARTITION BY ${partition} ` : '';
            q.select(knex.raw(`rank() OVER (${rankOver}ORDER BY ${rankCol} ${order.includes('DESC') ? 'DESC' : 'ASC'}) AS ??`, [alias]));
            break;
        case 'dense_rank':
            const denseRankCol = hasGroupBy ? `sum(${col})` : col;
            const denseRankOver = partition ? `PARTITION BY ${partition} ` : '';
            q.select(knex.raw(`dense_rank() OVER (${denseRankOver}ORDER BY ${denseRankCol} ${order.includes('DESC') ? 'DESC' : 'ASC'}) AS ??`, [alias]));
            break;
        case 'row_number':
            const rowNumberOver = buildOverClause(order, partition);
            q.select(knex.raw(`row_number() OVER (${rowNumberOver}) AS ??`, [alias]));
            break;
        default: throw 'Window fn not supported';
    }
}
function applyFilter(q, f, allowed, having = false, processedDimensions = []) {
    const builder = having ? q.having.bind(q) : q.where.bind(q);
    // Handle expression-based filters
    if (f.expr) {
        // Validate expression columns
        validateExpr(f.expr, allowed);
        // Create expression context for parsing
        const expressionContext = {
            allowedColumns: allowed,
            hasGroupBy: having, // Use having flag to determine if this is in HAVING clause
            processedDimensions
        };
        // Parse Metabase-style expressions or use raw SQL
        const sqlExpression = f.expr.includes('[') || (f.expr.includes('(') && !f.expr.startsWith('('))
            ? (0, expressions_1.parseExpression)(f.expr, expressionContext)
            : f.expr;
        builder(knex.raw(sqlExpression));
        return;
    }
    // Handle traditional field-based filters
    if (!f.field) {
        throw new Error('Filter must have either field or expr');
    }
    // alias OK in HAVING; otherwise enforce physical column
    if (!having && !allowed.includes(f.field))
        throw new Error(`Bad filter field ${f.field}`);
    if (!f.op) {
        throw new Error('Filter must have op when using field');
    }
    switch (f.op) {
        case 'eq':
            builder(f.field, '=', f.value);
            break;
        case 'neq':
            builder(f.field, '!=', f.value);
            break;
        case 'gt':
            builder(f.field, '>', f.value);
            break;
        case 'lt':
            builder(f.field, '<', f.value);
            break;
        case 'gte':
            builder(f.field, '>=', f.value);
            break;
        case 'lte':
            builder(f.field, '<=', f.value);
            break;
        case 'between':
            builder(f.field, 'between', f.value);
            break;
        case 'in':
            builder(f.field, 'in', f.value);
            break;
        case 'like':
            builder(f.field, 'like', f.value);
            break;
        default: throw 'Unsupported op';
    }
}
//# sourceMappingURL=queryBuilder.js.map