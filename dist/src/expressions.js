"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseExpression = parseExpression;
// Metabase-compatible expression parser and SQL generator
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
// Use the same knex instance as the main query builder
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
// Parse and convert Metabase expressions to SQL
function parseExpression(expr, context) {
    // Remove extra whitespace and normalize
    const normalized = expr.trim();
    // Handle function calls with regex
    const functionMatch = normalized.match(/^(\w+)\s*\((.*)\)$/);
    if (functionMatch) {
        const [, funcName, args] = functionMatch;
        return convertFunctionToSQL(funcName, args, context);
    }
    // Handle simple column references and replace all [column] references
    const columnReferenceRegex = /\[([^\]]+)\]/g;
    if (columnReferenceRegex.test(normalized)) {
        // Replace all [column] references with proper SQL column references
        return normalized.replace(columnReferenceRegex, (_, columnName) => {
            return knex.ref(columnName).toSQL().sql;
        });
    }
    // Handle string literals
    if (normalized.match(/^".*"$/)) {
        return normalized;
    }
    // Handle numeric literals
    if (normalized.match(/^-?\d+(\.\d+)?$/)) {
        return normalized;
    }
    // Return as-is for complex expressions (fallback)
    return normalized;
}
// Convert Metabase function calls to SQL
function convertFunctionToSQL(funcName, args, context) {
    const parsedArgs = parseArguments(args);
    switch (funcName.toLowerCase()) {
        // AGGREGATION FUNCTIONS
        case 'average':
            return `AVG(${parseExpression(parsedArgs[0], context)})`;
        case 'count':
            return parsedArgs.length > 0 ? `COUNT(${parseExpression(parsedArgs[0], context)})` : 'COUNT(*)';
        case 'countif':
            return `COUNT(CASE WHEN ${parseExpression(parsedArgs[0], context)} THEN 1 END)`;
        case 'distinct':
            return `COUNT(DISTINCT ${parseExpression(parsedArgs[0], context)})`;
        case 'distinctif':
            return `COUNT(DISTINCT CASE WHEN ${parseExpression(parsedArgs[1], context)} THEN ${parseExpression(parsedArgs[0], context)} END)`;
        case 'max':
            return `MAX(${parseExpression(parsedArgs[0], context)})`;
        case 'median':
            return `PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ${parseExpression(parsedArgs[0], context)})`;
        case 'min':
            return `MIN(${parseExpression(parsedArgs[0], context)})`;
        case 'percentile':
            return `PERCENTILE_CONT(${parsedArgs[1]}) WITHIN GROUP (ORDER BY ${parseExpression(parsedArgs[0], context)})`;
        case 'share':
            return `AVG(CASE WHEN ${parseExpression(parsedArgs[0], context)} THEN 1.0 ELSE 0.0 END)`;
        case 'standarddeviation':
            return `STDDEV(${parseExpression(parsedArgs[0], context)})`;
        case 'sum':
            return `SUM(${parseExpression(parsedArgs[0], context)})`;
        case 'sumif':
            return `SUM(CASE WHEN ${parseExpression(parsedArgs[1], context)} THEN ${parseExpression(parsedArgs[0], context)} ELSE 0 END)`;
        case 'variance':
            return `VARIANCE(${parseExpression(parsedArgs[0], context)})`;
        // LOGICAL FUNCTIONS
        case 'between':
            return `${parseExpression(parsedArgs[0], context)} BETWEEN ${parseExpression(parsedArgs[1], context)} AND ${parseExpression(parsedArgs[2], context)}`;
        case 'case':
        case 'if':
            return convertCaseExpression(parsedArgs, context);
        case 'coalesce':
            return `COALESCE(${parsedArgs.map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'in':
            return `${parseExpression(parsedArgs[0], context)} IN (${parsedArgs.slice(1).map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'isnull':
            return `${parseExpression(parsedArgs[0], context)} IS NULL`;
        case 'notin':
            return `${parseExpression(parsedArgs[0], context)} NOT IN (${parsedArgs.slice(1).map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'notnull':
            return `${parseExpression(parsedArgs[0], context)} IS NOT NULL`;
        // MATH FUNCTIONS
        case 'abs':
            return `ABS(${parseExpression(parsedArgs[0], context)})`;
        case 'ceil':
            return `CEIL(${parseExpression(parsedArgs[0], context)})`;
        case 'exp':
            return `EXP(${parseExpression(parsedArgs[0], context)})`;
        case 'floor':
            return `FLOOR(${parseExpression(parsedArgs[0], context)})`;
        case 'integer':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS INTEGER)`;
        case 'log':
            return `LOG(${parseExpression(parsedArgs[0], context)})`;
        case 'power':
            return `POWER(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)})`;
        case 'round':
            return parsedArgs.length > 1
                ? `ROUND(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)})`
                : `ROUND(${parseExpression(parsedArgs[0], context)})`;
        case 'sqrt':
            return `SQRT(${parseExpression(parsedArgs[0], context)})`;
        // STRING FUNCTIONS
        case 'concat':
            return `CONCAT(${parsedArgs.map(arg => parseExpression(arg, context)).join(', ')})`;
        case 'contains':
            const containsCaseSensitive = parsedArgs.length < 3 || parsedArgs[2] !== '"case-insensitive"';
            if (containsCaseSensitive) {
                return `${parseExpression(parsedArgs[0], context)} LIKE '%' || ${parseExpression(parsedArgs[1], context)} || '%'`;
            }
            else {
                return `LOWER(${parseExpression(parsedArgs[0], context)}) LIKE '%' || LOWER(${parseExpression(parsedArgs[1], context)}) || '%'`;
            }
        case 'doesnotcontain':
            const doesNotContainCaseSensitive = parsedArgs.length < 3 || parsedArgs[2] !== '"case-insensitive"';
            if (doesNotContainCaseSensitive) {
                return `${parseExpression(parsedArgs[0], context)} NOT LIKE '%' || ${parseExpression(parsedArgs[1], context)} || '%'`;
            }
            else {
                return `LOWER(${parseExpression(parsedArgs[0], context)}) NOT LIKE '%' || LOWER(${parseExpression(parsedArgs[1], context)}) || '%'`;
            }
        case 'endswith':
            const endsWithCaseSensitive = parsedArgs.length < 3 || parsedArgs[2] !== '"case-insensitive"';
            if (endsWithCaseSensitive) {
                return `${parseExpression(parsedArgs[0], context)} LIKE '%' || ${parseExpression(parsedArgs[1], context)}`;
            }
            else {
                return `LOWER(${parseExpression(parsedArgs[0], context)}) LIKE '%' || LOWER(${parseExpression(parsedArgs[1], context)})`;
            }
        case 'float':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS FLOAT)`;
        case 'isempty':
            return `(${parseExpression(parsedArgs[0], context)} IS NULL OR ${parseExpression(parsedArgs[0], context)} = '')`;
        case 'length':
            return `LENGTH(${parseExpression(parsedArgs[0], context)})`;
        case 'lower':
            return `LOWER(${parseExpression(parsedArgs[0], context)})`;
        case 'ltrim':
            return `LTRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'notempty':
            return `(${parseExpression(parsedArgs[0], context)} IS NOT NULL AND ${parseExpression(parsedArgs[0], context)} != '')`;
        case 'replace':
            return `REPLACE(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)}, ${parseExpression(parsedArgs[2], context)})`;
        case 'rtrim':
            return `RTRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'startswith':
            const startsWithCaseSensitive = parsedArgs.length < 3 || parsedArgs[2] !== '"case-insensitive"';
            if (startsWithCaseSensitive) {
                return `${parseExpression(parsedArgs[0], context)} LIKE ${parseExpression(parsedArgs[1], context)} || '%'`;
            }
            else {
                return `LOWER(${parseExpression(parsedArgs[0], context)}) LIKE LOWER(${parseExpression(parsedArgs[1], context)}) || '%'`;
            }
        case 'substring':
            return `SUBSTRING(${parseExpression(parsedArgs[0], context)}, ${parseExpression(parsedArgs[1], context)}, ${parseExpression(parsedArgs[2], context)})`;
        case 'text':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS TEXT)`;
        case 'trim':
            return `TRIM(${parseExpression(parsedArgs[0], context)})`;
        case 'upper':
            return `UPPER(${parseExpression(parsedArgs[0], context)})`;
        // DATE FUNCTIONS
        case 'date':
            return `DATE(${parseExpression(parsedArgs[0], context)})`;
        case 'datetime':
            return `CAST(${parseExpression(parsedArgs[0], context)} AS TIMESTAMP)`;
        case 'day':
            return `EXTRACT(DAY FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'hour':
            return `EXTRACT(HOUR FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'minute':
            return `EXTRACT(MINUTE FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'month':
            return `EXTRACT(MONTH FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'now':
            return 'NOW()';
        case 'quarter':
            return `EXTRACT(QUARTER FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'second':
            return `EXTRACT(SECOND FROM ${parseExpression(parsedArgs[0], context)})`;
        case 'weekday':
            return `EXTRACT(DOW FROM ${parseExpression(parsedArgs[0], context)}) + 1`;
        case 'year':
            return `EXTRACT(YEAR FROM ${parseExpression(parsedArgs[0], context)})`;
        default:
            // Fallback: return the function call as-is
            return `${funcName}(${args})`;
    }
}
// Parse function arguments, handling nested parentheses and quotes
function parseArguments(args) {
    if (!args.trim())
        return [];
    const result = [];
    let current = '';
    let depth = 0;
    let inQuotes = false;
    let quoteChar = '';
    for (let i = 0; i < args.length; i++) {
        const char = args[i];
        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
            current += char;
        }
        else if (inQuotes && char === quoteChar) {
            inQuotes = false;
            current += char;
        }
        else if (!inQuotes && char === '(') {
            depth++;
            current += char;
        }
        else if (!inQuotes && char === ')') {
            depth--;
            current += char;
        }
        else if (!inQuotes && char === ',' && depth === 0) {
            result.push(current.trim());
            current = '';
        }
        else {
            current += char;
        }
    }
    if (current.trim()) {
        result.push(current.trim());
    }
    return result;
}
// Convert CASE expression to SQL
function convertCaseExpression(args, context) {
    let sql = 'CASE';
    // Process pairs of condition/result
    for (let i = 0; i < args.length - 1; i += 2) {
        if (i + 1 < args.length) {
            sql += ` WHEN ${parseExpression(args[i], context)} THEN ${parseExpression(args[i + 1], context)}`;
        }
    }
    // Handle default case (odd number of arguments)
    if (args.length % 2 === 1) {
        sql += ` ELSE ${parseExpression(args[args.length - 1], context)}`;
    }
    sql += ' END';
    return sql;
}
//# sourceMappingURL=expressions.js.map