"use strict";
// Legacy expressions file - moved to expressions/parser.ts
// This file is kept for backward compatibility
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseExpression = void 0;
var parser_1 = require("./expressions/parser");
Object.defineProperty(exports, "parseExpression", { enumerable: true, get: function () { return parser_1.parseExpression; } });
//# sourceMappingURL=expressions.js.map