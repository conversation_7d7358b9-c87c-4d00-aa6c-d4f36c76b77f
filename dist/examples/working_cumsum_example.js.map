{"version": 3, "file": "working_cumsum_example.js", "sourceRoot": "", "sources": ["../../examples/working_cumsum_example.ts"], "names": [], "mappings": ";;AAAA,iFAAiF;AACjF,sDAA0D;AAE1D,KAAK,UAAU,yBAAyB;IACpC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAElE,wDAAwD;IACxD,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAE7E,MAAM,oBAAoB,GAAG;QACzB,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE;YACL,gCAAgC;YAChC,EAAE,KAAK,EAAE,OAAO,EAAE;YAClB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,aAAa,EAAE;YACxB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE1D,eAAe;YACf,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,qBAAqB,EAAE;YACzE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YAE7D,iDAAiD;YACjD,EAAE,IAAI,EAAE,gEAAgE,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAE1G,qBAAqB;YACrB,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,OAAgB,EAAE,KAAK,EAAE,eAAe,EAAE;SACnE;QACD,OAAO,EAAE;YACL,EAAE,IAAI,EAAE,kCAAkC,EAAE,EAAE,iBAAiB;YAC/D,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,2BAA2B;SAC9E;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,oBAAoB,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,qBAAqB;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,4BAA4B,CAAC,CAAC;IAC5E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,0CAA0C;IAC1C,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAElD,MAAM,wBAAwB,GAAG;QAC7B,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE;YACL,sBAAsB;YACtB,EAAE,KAAK,EAAE,OAAO,EAAE;YAClB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,aAAa,EAAE;YACxB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE1D,gBAAgB;YAChB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YACjE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YAE9D,2CAA2C;YAC3C;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;gBAC9C,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,6BAA6B;aACvC;YACD;gBACI,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;gBAClC,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,8BAA8B;aACxC;YAED,4CAA4C;YAC5C;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;SACJ;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE;SACzG;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,wBAAwB,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,qBAAqB;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,gCAAgC,CAAC,CAAC;IAChF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,iEAAiE;IACjE,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;IAC/E,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;IAC1E,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAEhE,MAAM,mBAAmB,GAAG;QACxB,KAAK,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE;QAChD,KAAK,EAAE;YACH;gBACI,KAAK,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC7C,IAAI,EAAE,MAAe;gBACrB,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;aACpD;SACJ;QACD,OAAO,EAAE;YACL,kCAAkC;YAClC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;YACrC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,aAAa,EAAE;YACjD,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE7D,mBAAmB;YACnB,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE;YACzE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YACjE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,eAAe,EAAE;YAElE,sCAAsC;YACtC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,qBAAqB,EAAE;YAExD,4CAA4C;YAC5C;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;YACD;gBACI,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0BAA0B;aACpC;YAED,wBAAwB;YACxB,EAAE,IAAI,EAAE,uCAAuC,EAAE,KAAK,EAAE,uBAAuB,EAAE;YACjF,EAAE,IAAI,EAAE,sEAAsE,EAAE,KAAK,EAAE,8BAA8B,EAAE;SAC1H;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;YACxE,EAAE,IAAI,EAAE,uCAAuC,EAAE;SACpD;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,mBAAmB,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QACzD,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,sCAAsC,CAAC,CAAC;IACtF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,8DAA8D;IAC9D,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAEzC,MAAM,uBAAuB,GAAG;QAC5B,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,OAAO,EAAE;YAClB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE1D,uBAAuB;YACvB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YACjE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YAE9D,mCAAmC;YACnC;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,KAAc;gBACnB,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;gBACrC,OAAO,EAAE,MAAM;gBACf,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,KAAK,EAAE,wBAAwB;aAClC;YAED,qBAAqB;YACrB;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,uBAAuB;aACjC;YAED,kCAAkC;YAClC;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,MAAe;gBACpB,OAAO,EAAE,aAAa;gBACtB,WAAW,EAAE,CAAC,MAAM,CAAC;gBACrB,KAAK,EAAE,yBAAyB;aACnC;YAED,yBAAyB;YACzB,EAAE,IAAI,EAAE,wDAAwD,EAAE,KAAK,EAAE,kBAAkB,EAAE;SAChG;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;YACrE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,EAAE;SACtD;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,uBAAuB,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,qBAAqB;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,+BAA+B,CAAC,CAAC;IAC/E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;AAClE,CAAC;AAED,0BAA0B;AAC1B,yBAAyB,EAAE;KACtB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,sBAAO,GAAE,CAAC;KACrB,IAAI,CAAC,GAAG,EAAE;IACP,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAC5E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACX,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}