{"version": 3, "file": "working_cumsum_example.js", "sourceRoot": "", "sources": ["../../examples/working_cumsum_example.ts"], "names": [], "mappings": ";;AAAA,wCAAwC;AACxC,sDAA8D;AAE9D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;AAEjD,yDAAyD;AACzD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACpD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAG,4CAA4C;IACjF,UAAU,EAAE;QACR;YACI,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,CAAC,UAAU,CAAC;YAC3B,SAAS,EAAE,MAAM;YACjB,OAAO,EAAE,eAAe;SAC3B;QACD,wCAAwC;KAC3C;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE;QAChD,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE;KACvD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,kEAAkE;AAClE,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AACrD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE;QACV,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,EAAG,sCAAsC;QAChF,UAAU;KACb;IACD,UAAU,EAAE;QACR;YACI,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,eAAe;SAC3B;QACD;YACI,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,CAAC,UAAU,CAAC;YAC3B,SAAS,EAAE,YAAY,EAAG,8BAA8B;YACxD,OAAO,EAAE,uBAAuB;SACnC;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,KAAc,EAAE;QACtD,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE;KACvD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,2DAA2D;AAC3D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;AAChD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,CAAC,UAAU,CAAC;IAC1B,UAAU,EAAE;QACR;YACI,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,aAAa;SACzB;QACD;YACI,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,UAAU,EAAG,8BAA8B;YACtD,OAAO,EAAE,2BAA2B;SACvC;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE;KACvD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,CAAC,KAAK,IAAI,EAAE;IACR,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAmB,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}