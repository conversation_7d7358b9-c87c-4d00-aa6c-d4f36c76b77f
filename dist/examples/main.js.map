{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../examples/main.ts"], "names": [], "mappings": ";;AAAA,4DAA4D;AAC5D,sDAA0D;AAE1D,mFAAmF;AACnF,IAAI,GAAG,GAAG;IACN,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,sDAAsD;QACtD,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAgB,EAAE,OAAO,EAAE,OAAO,EAAE;QAClE,gBAAgB;QAChB,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB,cAAc;QACd;YACI,OAAO,EAAE,WAAW;YACpB,KAAK,EAAE,KAAc;YACrB,OAAO,EAAE,mBAAmB;SAC/B;QACD,oCAAoC;QACpC;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,QAAiB;YACxB,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAG,uCAAuC;YAC9E,SAAS,EAAE,OAAO,EAAG,uCAAuC;YAC5D,OAAO,EAAE,eAAe;SAC3B;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,KAAc,EAAE,EAAG,8BAA8B;QACnF,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE,CAAE,uBAAuB;KACjF;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAGF,CAAC,KAAK,IAAI,EAAE;IACR,IAAI,CAAC;QACD,MAAM,CAAC,GAAG,IAAA,yBAAU,EAAC,GAAG,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAE,+BAA+B;QACnE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAc,qBAAqB;QACxD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;YAAS,CAAC;QACP,6DAA6D;QAC7D,MAAM,IAAA,sBAAO,GAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}