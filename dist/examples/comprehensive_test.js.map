{"version": 3, "file": "comprehensive_test.js", "sourceRoot": "", "sources": ["../../examples/comprehensive_test.ts"], "names": [], "mappings": ";;AAAA,gDAAgD;AAChD,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;AAE7D,CAAC,KAAK,IAAI,EAAE;IACR,IAAI,CAAC;QACD,+CAA+C;QAC/C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,YAAY,EAAE;gBAChE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,WAAW,EAAE;aAC9D;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,gDAAgD;QAChD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,IAAI,EAAE,4BAA4B,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBACjE,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE;aAC9C;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QAEtE,uBAAuB;QACvB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAgB,EAAE;gBAC5C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;aACrE;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,6EAA6E;QAC7E,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,EAA2B,oCAAoC;gBAChF,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;gBAC7D;oBACI,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,QAAiB;oBACtB,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,OAAO,EAAE,MAAM,EAAyB,oCAAoC;oBAC5E,KAAK,EAAE,eAAe;iBACzB;aACJ;YACD,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;aACxE;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAE9E,qDAAqD;QACrD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAgB,EAAE;gBAC5C,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;gBAClE;oBACI,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,QAAiB;oBACtB,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,OAAO,EAAE,YAAY;oBACrB,KAAK,EAAE,eAAe;iBACzB;aACJ;YACD,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;aACxE;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QAEhF,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG;YACV,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAC/B,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,YAAY,EAAE;aACnE;YACD,KAAK,EAAE,CAAC;SACX,CAAC;QACF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAEzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;YAAS,CAAC;QACP,MAAM,IAAA,sBAAO,GAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}