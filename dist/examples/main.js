"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// demo.ts
const queryBuilder_1 = require("../src/queryBuilder");
// import cfg from './config.json';
// Example showing date binning functionality
let cfg = {
    "table": "progress_history",
    "dimensions": [
        // New date binning syntax - group by month
        { "field": "Date", "dateBin": "month", "alias": "month" },
        // Mix with regular string dimensions
        "Sublayer"
    ],
    "measures": [
        {
            "agg": "sum",
            "field": "Work done",
            "alias": "monthly_work_done"
        },
        // {
        //     "agg": "sum",
        //     "field": "Scope",
        //     "alias": "monthly_scope"
        // },
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer", "month"], // Partition by both Sublayer and month
            "orderBy": "month", // Order by month within each partition
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "month", "direction": "asc" }, // Note: use the generated alias
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 20
};
(async () => {
    try {
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative()); // see generated SQL & bindings
        console.log('\nSQL formatted:');
        console.log(q.toSQL().sql);
        console.log('\nExecuting query...');
        const rows = await q; // execute & get rows
        console.table(rows);
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        // Close the database connection to allow the process to exit
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
//# sourceMappingURL=main.js.map