{"version": 3, "file": "date_binning_examples.js", "sourceRoot": "", "sources": ["../../examples/date_binning_examples.ts"], "names": [], "mappings": ";;AAAA,2FAA2F;AAC3F,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;AAE/C,qCAAqC;AACrC,MAAM,QAAQ,GAAG;IACb;QACI,IAAI,EAAE,aAAa;QACnB,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAc,EAAE;gBAC1C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,YAAY,EAAE;aACnE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,cAAc;QACpB,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE;gBAC3C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;aACpE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,eAAe;QACrB,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAgB,EAAE;gBAC5C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;aACrE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,iBAAiB;QACvB,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,SAAkB,EAAE;gBAC9C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,gBAAgB,EAAE;aACvE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,cAAc;QACpB,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE;gBAC3C,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;aACpE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,oCAAoC;QAC1C,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,aAAsB,EAAE;gBAClD,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,iBAAiB,EAAE;aACxE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,sBAAsB;QAC5B,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,eAAwB,EAAE;gBACpD,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,eAAe,EAAE;aACtE;YACD,KAAK,EAAE,EAAE;SACZ;KACJ;IACD;QACI,IAAI,EAAE,uBAAuB;QAC7B,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,iBAA0B,EAAE;gBACtD,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,iBAAiB,EAAE;aACxE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,sBAAsB;QAC5B,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,SAAkB,EAAE,KAAK,EAAE,mBAAmB,EAAE;gBAC1E,EAAE,KAAK,EAAE,UAAU,EAAE;gBACrB,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;aAChE;YACD,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,KAAc,EAAE,CAAC;YACrE,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,2BAA2B;QACjC,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAChC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,OAAgB,EAAE,KAAK,EAAE,cAAc,EAAE;aAClE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,6CAA6C;QACnD,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;gBACxD,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,WAAW,EAAE;aAC9D;YACD,KAAK,EAAE,CAAC;SACX;KACJ;IACD;QACI,IAAI,EAAE,gCAAgC;QACtC,MAAM,EAAE;YACJ,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE;gBACL,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAgB,EAAE;gBAC5C,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAC/B,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,YAAY,EAAE;aACnE;YACD,KAAK,EAAE,CAAC;SACX;KACJ;CACJ,CAAC;AAEF,gCAAgC;AAChC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;IAChC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;IAC9C,MAAM,CAAC,GAAG,IAAA,yBAAU,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACnC,OAAO,CAAC,GAAG,CAAC,sFAAsF,CAAC,CAAC;AACpG,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;AACnE,OAAO,CAAC,GAAG,CAAC,0EAA0E,CAAC,CAAC;AACxF,OAAO,CAAC,GAAG,CAAC,gFAAgF,CAAC,CAAC;AAC9F,OAAO,CAAC,GAAG,CAAC,sFAAsF,CAAC,CAAC;AACpG,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;AAC/E,OAAO,CAAC,GAAG,CAAC,sFAAsF,CAAC,CAAC;AACpG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;AAC7C,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;AACpF,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;AAClG,OAAO,CAAC,GAAG,CAAC,8EAA8E,CAAC,CAAC;AAC5F,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC,CAAC;AACvF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAClD,OAAO,CAAC,GAAG,CAAC,kGAAkG,CAAC,CAAC;AAEhH,oBAAoB;AACpB,IAAA,sBAAO,GAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC"}