{"version": 3, "file": "complex_progress_analysis.js", "sourceRoot": "", "sources": ["../../examples/complex_progress_analysis.ts"], "names": [], "mappings": ";;AAAA,2DAA2D;AAC3D,oIAAoI;AACpI,sDAA0D;AAE1D,KAAK,UAAU,0BAA0B;IACrC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IAExE,wDAAwD;IACxD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC5D,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;IAC1E,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IAEnE,MAAM,qBAAqB,GAAG;QAC1B,wBAAwB;QACxB,KAAK,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE;QAEhD,4DAA4D;QAC5D,KAAK,EAAE;YACH;gBACI,KAAK,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC7C,IAAI,EAAE,MAAe;gBACrB,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;aACpD;SACJ;QAED,OAAO,EAAE;YACL,qCAAqC;YACrC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;YACrC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,aAAa,EAAE;YACjD,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE7D,kDAAkD;YAClD,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,qBAAqB,EAAE;YAC5E,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAC1E,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,yBAAyB,EAAE;YAE5E,iDAAiD;YACjD;gBACI,IAAI,EAAE,sEAAsE;gBAC5E,KAAK,EAAE,4BAA4B;aACtC;YACD;gBACI,IAAI,EAAE,sEAAsE;gBAC5E,KAAK,EAAE,6BAA6B;aACvC;YAED,+CAA+C;YAC/C,2BAA2B;YAC3B;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0BAA0B;aACpC;YAED,8BAA8B;YAC9B;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;gBACxC,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,6BAA6B;aACvC;YAED,8BAA8B;YAC9B;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC;gBACvD,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,6BAA6B;aACvC;YAED,iDAAiD;YACjD;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC;gBACzE,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,gCAAgC;aAC1C;YAED,kDAAkD;YAClD;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;YAED,oCAAoC;YACpC;gBACI,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,yBAAyB;aACnC;YACD;gBACI,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0BAA0B;aACpC;YAED,4CAA4C;YAC5C,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAC1D,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAC1D,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,oBAAoB,EAAE;YAEvD,6BAA6B;YAC7B,sCAAsC;YACtC,EAAE,IAAI,EAAE,uCAAuC,EAAE,KAAK,EAAE,uBAAuB,EAAE;YACjF,EAAE,IAAI,EAAE,mCAAmC,EAAE,KAAK,EAAE,wBAAwB,EAAE;YAE9E,qBAAqB;YACrB,EAAE,IAAI,EAAE,8DAA8D,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAExG,oDAAoD;YACpD;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,KAAc;gBACnB,uDAAuD;gBACvD,KAAK,EAAE,wBAAwB;aAClC;SACJ;QAED,oCAAoC;QACpC,OAAO,EAAE;YACL,EAAE,IAAI,EAAE,uCAAuC,EAAE,EAAE,oBAAoB;YACvE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,gCAAgC;YACxF,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,eAAe,CAAC,EAAE;SAC5G;QAED,iCAAiC;QACjC,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,KAAc,EAAE;YACpD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QAED,KAAK,EAAE,EAAE,CAAC,sBAAsB;KACnC,CAAC;IAEF,sCAAsC;IACtC,MAAM,YAAY,GAAG,IAAA,yBAAU,EAAC,qBAAqB,CAAC,CAAC;IAEvD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC;QAEnC,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,+BAA+B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,mBAAmB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACpG,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACvG,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,0BAA0B,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YAEhI,OAAO,CAAC,GAAG,CAAC,0BAA0B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,sBAAsB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACtF,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAEzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAClD,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAChF,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;IACzF,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IACnE,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAC3E,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;AAC3E,CAAC;AAED,+BAA+B;AAC/B,0BAA0B,EAAE;KACvB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,sBAAO,GAAE,CAAC;KACrB,IAAI,CAAC,GAAG,EAAE;IACP,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACX,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;IAC9D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}