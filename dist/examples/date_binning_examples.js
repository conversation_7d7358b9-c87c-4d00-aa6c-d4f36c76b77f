"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Comprehensive examples of date binning functionality
const queryBuilder_1 = require("../src/queryBuilder");
const queryBuilder_2 = require("../src/queryBuilder");
console.log('=== Date Binning Examples ===\n');
// All supported date binning options
const examples = [
    {
        name: "Day Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day" }],
            measures: [{ agg: "sum", field: "Work done", alias: "daily_work" }],
            limit: 3
        }
    },
    {
        name: "Week Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "week" }],
            measures: [{ agg: "sum", field: "Work done", alias: "weekly_work" }],
            limit: 3
        }
    },
    {
        name: "Month Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "month" }],
            measures: [{ agg: "sum", field: "Work done", alias: "monthly_work" }],
            limit: 3
        }
    },
    {
        name: "Quarter Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "quarter" }],
            measures: [{ agg: "sum", field: "Work done", alias: "quarterly_work" }],
            limit: 3
        }
    },
    {
        name: "Year Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "yearly_work" }],
            limit: 3
        }
    },
    {
        name: "Day of Week (0=Sunday, 6=Saturday)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_week" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_weekday" }],
            limit: 7
        }
    },
    {
        name: "Day of Month (1-31)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_month" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_day" }],
            limit: 5
        }
    },
    {
        name: "Day of Year (1-366)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_day_of_year" }],
            limit: 5
        }
    },
    {
        name: "Week of Year (1-53)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "week_of_year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_week" }],
            limit: 5
        }
    },
    {
        name: "Month of Year (1-12)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "month_of_year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_month" }],
            limit: 12
        }
    },
    {
        name: "Quarter of Year (1-4)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "quarter_of_year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_quarter" }],
            limit: 4
        }
    },
    {
        name: "Year of Era (Full Year Number)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "year_of_era" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_year" }],
            limit: 5
        }
    },
    {
        name: "Custom Alias Example",
        config: {
            table: "progress_history",
            dimensions: [
                { field: "Date", dateBin: "quarter", alias: "reporting_quarter" },
                "Sublayer"
            ],
            measures: [{ agg: "sum", field: "Scope", alias: "total_scope" }],
            orderBy: [{ field: "reporting_quarter", direction: "asc" }],
            limit: 5
        }
    },
    {
        name: "Numeric Binning - 10 bins",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Scope", binCount: 10 }],
            measures: [{ agg: "count", field: "Date", alias: "record_count" }],
            limit: 5
        }
    },
    {
        name: "Numeric Binning - 50 bins with custom alias",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Scope", binCount: 50, alias: "scope_buckets" }],
            measures: [{ agg: "avg", field: "Scope", alias: "avg_scope" }],
            limit: 5
        }
    },
    {
        name: "Mixed Date and Numeric Binning",
        config: {
            table: "progress_history",
            dimensions: [
                { field: "Date", dateBin: "month" },
                { field: "Scope", binCount: 5 }
            ],
            measures: [{ agg: "sum", field: "Work done", alias: "total_work" }],
            limit: 5
        }
    }
];
// Generate SQL for each example
examples.forEach((example, index) => {
    console.log(`${index + 1}. ${example.name}:`);
    const q = (0, queryBuilder_1.buildQuery)(example.config);
    console.log(`   SQL: ${q.toSQL().sql}`);
    console.log('');
});
console.log('=== Usage Notes ===');
console.log('• Use the old string format: "Date" for raw date columns');
console.log('• Use the new object format: { field: "Date", dateBin: "month" } for date binning');
console.log('• Generated aliases follow the pattern: fieldName_dateBin (e.g., "Date_month")');
console.log('• You can override the alias: { field: "Date", dateBin: "month", alias: "my_month" }');
console.log('• Mix old and new formats: ["Date", { field: "Date", dateBin: "month" }, "Sublayer"]');
console.log('• Use "dont_bin" to explicitly disable binning while using object syntax');
console.log('');
console.log('=== Numeric Binning Notes ===');
console.log('• Use binCount for numeric columns: { field: "Scope", binCount: 10 }');
console.log('• Generated aliases follow the pattern: fieldName_bin_count (e.g., "Scope_bin_10")');
console.log('• Custom aliases work: { field: "Scope", binCount: 50, alias: "my_buckets" }');
console.log('• Uses PostgreSQL WIDTH_BUCKET function for automatic range calculation');
console.log('• Bin numbers start from 1 (not 0)');
console.log('• Mix with date binning: [{ field: "Date", dateBin: "month" }, { field: "Scope", binCount: 10 }]');
// Clean up and exit
(0, queryBuilder_2.cleanup)().then(() => process.exit(0));
//# sourceMappingURL=date_binning_examples.js.map