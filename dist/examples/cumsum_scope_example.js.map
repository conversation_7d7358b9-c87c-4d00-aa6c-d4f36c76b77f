{"version": 3, "file": "cumsum_scope_example.js", "sourceRoot": "", "sources": ["../../examples/cumsum_scope_example.ts"], "names": [], "mappings": ";;AAAA,+FAA+F;AAC/F,sDAA0D;AAE1D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AAElD,gDAAgD;AAChD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;AACtD,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,KAAc;YACrB,OAAO,EAAE,aAAa;SACzB;QACD;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,QAAiB;YACxB,aAAa,EAAE,CAAC,UAAU,CAAC;YAC3B,SAAS,EAAE,MAAM;YACjB,OAAO,EAAE,eAAe;SAC3B;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE;KACxD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,yDAAyD;AACzD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;AACtE,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE;QACnB,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,QAAiB;YACxB,aAAa,EAAE,CAAC,UAAU,CAAC;YAC3B,SAAS,EAAE,MAAM;YACjB,OAAO,EAAE,eAAe;SAC3B;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE;QACjD,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,KAAc,EAAE;KACxD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,mDAAmD;AACnD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;AAC3D,IAAI,IAAI,GAAG;IACP,OAAO,EAAE,kBAAkB;IAC3B,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,MAAM,EAAE;QACnB,EAAE,OAAO,EAAE,UAAU,EAAE;QACvB;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,KAAc;YACrB,OAAO,EAAE,OAAO;SACnB;QACD;YACI,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,QAAiB;YACxB,SAAS,EAAE,MAAM;YACjB,OAAO,EAAE,sBAAsB;SAClC;KACJ;IACD,SAAS,EAAE;QACP,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,IAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE;KAC9E;IACD,SAAS,EAAE;QACP,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAc,EAAE;KACpD;IACD,OAAO,EAAE,EAAE;CACd,CAAC;AAEF,CAAC,KAAK,IAAI,EAAE;IACR,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;YAAS,CAAC;QACP,MAAM,IAAA,sBAAO,GAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}