{"version": 3, "file": "working_complex_example.js", "sourceRoot": "", "sources": ["../../examples/working_complex_example.ts"], "names": [], "mappings": ";;AAAA,8DAA8D;AAC9D,sDAA0D;AAE1D,KAAK,UAAU,wBAAwB;IACnC,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;IAEjE,6DAA6D;IAC7D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;IAC1E,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,MAAM,kBAAkB,GAAG;QACvB,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE;YACL,wBAAwB;YACxB,EAAE,KAAK,EAAE,OAAO,EAAE;YAClB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,aAAa,EAAE;YACxB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE1D,eAAe;YACf,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,qBAAqB,EAAE;YACzE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YAE7D,yBAAyB;YACzB,EAAE,IAAI,EAAE,gEAAgE,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAE1G,gDAAgD;YAChD;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,OAAO,CAAC;gBACtB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,qBAAqB;aAC/B;YACD;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;gBAClC,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;YACD;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;gBAC9C,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;YACD;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,mBAAmB;aAC7B;SACJ;QACD,OAAO,EAAE;YACL,EAAE,IAAI,EAAE,kCAAkC,EAAE;YAC5C,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,EAAE;SACtD;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,kBAAkB,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,yBAAyB,CAAC,CAAC;IACzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACtG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,qCAAqC;IACrC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,MAAM,UAAU,GAAG;QACf,KAAK,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE;QAChD,KAAK,EAAE;YACH;gBACI,KAAK,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC7C,IAAI,EAAE,OAAgB;gBACtB,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE;aACpD;SACJ;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;YACrC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;YAC3C,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,aAAa,EAAE;YACjD,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE7D,4BAA4B;YAC5B,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YACpE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YACjE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,eAAe,EAAE;YAElE,0BAA0B;YAC1B,EAAE,IAAI,EAAE,sEAAsE,EAAE,KAAK,EAAE,uBAAuB,EAAE;YAChH,EAAE,IAAI,EAAE,sEAAsE,EAAE,KAAK,EAAE,gBAAgB,EAAE;YAEzG,8BAA8B;YAC9B;gBACI,KAAK,EAAE,cAAc;gBACrB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,wBAAwB;aAClC;YACD;gBACI,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,aAAa,CAAC;gBAC5B,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0BAA0B;aACpC;YAED,oBAAoB;YACpB,EAAE,IAAI,EAAE,uCAAuC,EAAE,KAAK,EAAE,uBAAuB,EAAE;YACjF,EAAE,IAAI,EAAE,8DAA8D,EAAE,KAAK,EAAE,kBAAkB,EAAE;SACtG;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;YACxE,EAAE,IAAI,EAAE,uCAAuC,EAAE;SACpD;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAc,EAAE;YAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,UAAU,CAAC,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,mBAAmB,CAAC,CAAC;QAE/D,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,gBAAgB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YACxH,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3G,OAAO,CAAC,GAAG,CAAC,mCAAmC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,sCAAsC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9F,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,qDAAqD;IACrD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAE9C,MAAM,cAAc,GAAG;QACnB,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,OAAO,EAAE;YAClB,EAAE,KAAK,EAAE,UAAU,EAAE;YACrB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE;YAE1D,kBAAkB;YAClB,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,aAAa,EAAE;YACjE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,KAAc,EAAE,KAAK,EAAE,cAAc,EAAE;YAE9D,yBAAyB;YACzB;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,KAAc;gBACnB,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;gBACrC,OAAO,EAAE,MAAM;gBACf,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,KAAK,EAAE,mBAAmB;aAC7B;YAED,oBAAoB;YACpB;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,QAAiB;gBACtB,WAAW,EAAE,CAAC,UAAU,CAAC;gBACzB,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,iBAAiB;aAC3B;YAED,uCAAuC;YACvC;gBACI,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,MAAe;gBACpB,OAAO,EAAE,aAAa;gBACtB,WAAW,EAAE,CAAC,MAAM,CAAC;gBACrB,KAAK,EAAE,aAAa;aACvB;YAED,qBAAqB;YACrB,EAAE,IAAI,EAAE,wDAAwD,EAAE,KAAK,EAAE,YAAY,EAAE;SAC1F;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,iBAAiB,EAAE;YACrE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,IAAa,EAAE,KAAK,EAAE,CAAC,EAAE;SACtD;QACD,OAAO,EAAE;YACL,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAc,EAAE;YACjD,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAc,EAAE;SAChD;QACD,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,yBAAU,EAAC,cAAc,CAAC,CAAC;IACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAElB,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,+BAA+B,CAAC,CAAC;IAC/E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5G,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC;IAC1F,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IACpE,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;AACrE,CAAC;AAED,kCAAkC;AAClC,wBAAwB,EAAE;KACrB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,sBAAO,GAAE,CAAC;KACrB,IAAI,CAAC,GAAG,EAAE;IACP,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IACnE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACX,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}