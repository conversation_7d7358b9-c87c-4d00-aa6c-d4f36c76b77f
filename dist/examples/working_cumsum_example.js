"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Complex Progress Tracking Example with Percentages, Cumulative Sums, and Joins
const queryBuilder_1 = require("../src/queryBuilder");
async function runComplexProgressExample() {
    console.log('🚀 === COMPLEX PROGRESS TRACKING ANALYSIS === 🚀\n');
    // Example 1: Weekly Progress with Percentage Completion
    console.log('📊 1. Weekly Progress with Percentage Completion:');
    console.log('   - Shows work done this week compared to total scope');
    console.log('   - Calculates percentage completion');
    console.log('   - Groups by Layer, Sublayer, Activity, Subactivity, Week\n');
    const weeklyProgressConfig = {
        table: "progress_history",
        columns: [
            // Hierarchical grouping columns
            { field: "Layer" },
            { field: "Sublayer" },
            { field: "Activity" },
            { field: "Subactivity" },
            { field: "Date", dateBin: "week", alias: "week" },
            // Work metrics
            { field: "Work done", agg: "sum", alias: "work_done_this_week" },
            { field: "Scope", agg: "sum", alias: "total_scope" },
            // Percentage calculation using custom expression
            { expr: "round((sum([Work done]) * 100.0 / nullif(sum([Scope]), 0)), 2)", alias: "completion_percentage" },
            // Additional metrics
            { field: "Date", agg: "count", alias: "records_count" }
        ],
        filters: [
            { expr: 'EXTRACT(YEAR FROM "Date") = 2025' }, // Only 2025 data
            { field: "Scope", op: "gt", value: 0 } // Exclude zero scope items
        ],
        orderBy: [
            { column: "Layer", direction: "asc" },
            { column: "Sublayer", direction: "asc" },
            { column: "week", direction: "asc" }
        ],
        limit: 20
    };
    const q1 = (0, queryBuilder_1.buildQuery)(weeklyProgressConfig);
    console.log('Generated SQL:');
    console.log(q1.toSQL().sql);
    console.log('\n');
    try {
        const results1 = await q1;
        console.log('📈 Weekly Progress Results:');
        console.table(results1.slice(0, 10)); // Show first 10 rows
        console.log(`✅ Retrieved ${results1.length} weekly progress records\n`);
    }
    catch (error) {
        console.error('❌ Weekly progress query failed:', error);
        console.log('\n');
    }
    // Example 2: Cumulative Progress Tracking
    console.log('📈 2. Cumulative Progress Tracking:');
    console.log('   - Running totals partitioned by Layer, Sublayer, Activity');
    console.log('   - Shows progressive completion over time');
    console.log('   - Multiple cumulative metrics\n');
    const cumulativeProgressConfig = {
        table: "progress_history",
        columns: [
            // Grouping dimensions
            { field: "Layer" },
            { field: "Sublayer" },
            { field: "Activity" },
            { field: "Subactivity" },
            { field: "Date", dateBin: "week", alias: "week" },
            // Weekly totals
            { field: "Work done", agg: "sum", alias: "weekly_work" },
            { field: "Scope", agg: "sum", alias: "weekly_scope" },
            // Cumulative sums partitioned by hierarchy
            {
                field: "Work done",
                agg: "cumsum",
                partitionBy: ["Layer", "Sublayer", "Activity"],
                orderBy: "week",
                alias: "cumulative_work_by_activity"
            },
            {
                field: "Scope",
                agg: "cumsum",
                partitionBy: ["Layer", "Sublayer"],
                orderBy: "week",
                alias: "cumulative_scope_by_sublayer"
            },
            // Global cumulative (across all activities)
            {
                field: "Work done",
                agg: "cumsum",
                orderBy: "week",
                alias: "global_cumulative_work"
            }
        ],
        filters: [
            { field: "Subactivity", op: "in", value: ["Piles Installed", "Excavation", "Concrete Work"] }
        ],
        orderBy: [
            { column: "Layer", direction: "asc" },
            { column: "Sublayer", direction: "asc" },
            { column: "Activity", direction: "asc" },
            { column: "week", direction: "asc" }
        ],
        limit: 25
    };
    const q2 = (0, queryBuilder_1.buildQuery)(cumulativeProgressConfig);
    console.log('Generated SQL:');
    console.log(q2.toSQL().sql);
    console.log('\n');
    try {
        const results2 = await q2;
        console.log('📊 Cumulative Progress Results:');
        console.table(results2.slice(0, 10)); // Show first 10 rows
        console.log(`✅ Retrieved ${results2.length} cumulative progress records\n`);
    }
    catch (error) {
        console.error('❌ Cumulative progress query failed:', error);
        console.log('\n');
    }
    // Example 3: Complex JOIN with Progress Data and Date Extraction
    console.log('🔗 3. JOIN with Progress Data - Extract Estimated Finish Dates:');
    console.log('   - Joins progress_history with progress_data on Sublayer');
    console.log('   - Extracts estimated finish dates');
    console.log('   - Combines actual progress with planned dates');
    console.log('   - Shows variance between actual and planned\n');
    const joinWithDatesConfig = {
        table: { name: "progress_history", alias: "ph" },
        joins: [
            {
                table: { name: "progress_data", alias: "pd" },
                type: "left",
                on: { left: "ph.Sublayer", right: "pd.Sublayer" }
            }
        ],
        columns: [
            // Hierarchy from progress_history
            { field: "ph.Layer", alias: "layer" },
            { field: "ph.Sublayer", alias: "sublayer" },
            { field: "ph.Activity", alias: "activity" },
            { field: "ph.Subactivity", alias: "subactivity" },
            { field: "ph.Date", dateBin: "week", alias: "week" },
            // Progress metrics
            { field: "ph.Work done", agg: "sum", alias: "actual_work_done" },
            { field: "ph.Scope", agg: "sum", alias: "actual_scope" },
            { field: "pd.Scope", agg: "sum", alias: "planned_scope" },
            // Date extractions from progress_data
            { expr: "max([pd.Date])", alias: "latest_planned_date" },
            // Cumulative tracking with cross-table data
            {
                field: "ph.Work done",
                agg: "cumsum",
                partitionBy: ["ph.Sublayer"],
                orderBy: "week",
                alias: "cumulative_actual_work"
            },
            {
                field: "pd.Scope",
                agg: "cumsum",
                partitionBy: ["ph.Sublayer"],
                orderBy: "week",
                alias: "cumulative_planned_scope"
            },
            // Variance calculations
            { expr: "sum([ph.Work done]) - sum([pd.Scope])", alias: "work_vs_plan_variance" },
            { expr: "round((sum([ph.Work done]) * 100.0 / nullif(sum([pd.Scope]), 0)), 2)", alias: "actual_vs_planned_percentage" }
        ],
        filters: [
            { field: "ph.Subactivity", op: "eq", value: "Piles Installed" },
            { expr: 'EXTRACT(YEAR FROM "ph"."Date") = 2025' }
        ],
        orderBy: [
            { column: "layer", direction: "asc" },
            { column: "sublayer", direction: "asc" },
            { column: "week", direction: "asc" }
        ],
        limit: 30
    };
    const q3 = (0, queryBuilder_1.buildQuery)(joinWithDatesConfig);
    console.log('Generated SQL:');
    console.log(q3.toSQL().sql);
    console.log('\n');
    try {
        const results3 = await q3;
        console.log('🔗 JOIN with Dates Results:');
        console.table(results3.slice(0, 8)); // Show first 8 rows
        console.log(`✅ Retrieved ${results3.length} joined records with date analysis\n`);
    }
    catch (error) {
        console.error('❌ JOIN query failed:', error);
        console.log('\n');
    }
    // Example 4: Advanced Analytics - Rolling Averages and Trends
    console.log('📊 4. Advanced Analytics - Rolling Averages and Performance Trends:');
    console.log('   - 4-week rolling averages');
    console.log('   - Performance trends and rankings');
    console.log('   - Efficiency metrics\n');
    const advancedAnalyticsConfig = {
        table: "progress_history",
        columns: [
            { field: "Layer" },
            { field: "Sublayer" },
            { field: "Date", dateBin: "week", alias: "week" },
            // Current week metrics
            { field: "Work done", agg: "sum", alias: "weekly_work" },
            { field: "Scope", agg: "sum", alias: "weekly_scope" },
            // Rolling averages (4-week window)
            {
                field: "Work done",
                agg: "avg",
                frame: { preceding: 3, following: 0 },
                orderBy: "week",
                partitionBy: ["Sublayer"],
                alias: "rolling_4week_avg_work"
            },
            // Cumulative metrics
            {
                field: "Work done",
                agg: "cumsum",
                partitionBy: ["Sublayer"],
                orderBy: "week",
                alias: "total_cumulative_work"
            },
            // Ranking and performance metrics
            {
                field: "Work done",
                agg: "rank",
                orderBy: "weekly_work",
                partitionBy: ["week"],
                alias: "weekly_performance_rank"
            },
            // Efficiency calculation
            { expr: "round((sum([Work done]) / nullif(sum([Scope]), 0)), 4)", alias: "efficiency_ratio" }
        ],
        filters: [
            { field: "Subactivity", op: "eq", value: "Piles Installed" },
            { field: "Work done", op: "gt", value: 0 }
        ],
        orderBy: [
            { column: "Sublayer", direction: "asc" },
            { column: "week", direction: "asc" }
        ],
        limit: 25
    };
    const q4 = (0, queryBuilder_1.buildQuery)(advancedAnalyticsConfig);
    console.log('Generated SQL:');
    console.log(q4.toSQL().sql);
    console.log('\n');
    try {
        const results4 = await q4;
        console.log('📈 Advanced Analytics Results:');
        console.table(results4.slice(0, 10)); // Show first 10 rows
        console.log(`✅ Retrieved ${results4.length} advanced analytics records\n`);
    }
    catch (error) {
        console.error('❌ Advanced analytics query failed:', error);
        console.log('\n');
    }
    console.log('🎉 === COMPLEX PROGRESS TRACKING ANALYSIS COMPLETE === 🎉');
    console.log('\n📋 Summary of Features Demonstrated:');
    console.log('✅ Hierarchical grouping (Layer → Sublayer → Activity → Subactivity)');
    console.log('✅ Weekly date binning and time-based analysis');
    console.log('✅ Percentage calculations (work done vs scope)');
    console.log('✅ Multiple cumulative sum patterns with different partitioning');
    console.log('✅ Complex JOINs with progress_data table');
    console.log('✅ Date extraction and planned vs actual analysis');
    console.log('✅ Rolling averages and performance rankings');
    console.log('✅ Variance calculations and efficiency metrics');
    console.log('✅ Advanced window functions with custom frames');
}
// Run the complex example
runComplexProgressExample()
    .then(() => (0, queryBuilder_1.cleanup)())
    .then(() => {
    console.log('\n✅ Complex progress tracking example completed successfully');
    process.exit(0);
})
    .catch(error => {
    console.error('\n❌ Complex example failed:', error);
    process.exit(1);
});
//# sourceMappingURL=working_cumsum_example.js.map