{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true}, "include": ["src/**/*", "examples/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}