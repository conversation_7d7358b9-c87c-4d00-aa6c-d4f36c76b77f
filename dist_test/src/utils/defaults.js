"use strict";
// Default value generators and utilities
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateColumnAlias = generateColumnAlias;
exports.applyFilterDefaults = applyFilterDefaults;
exports.applyOrderByDefaults = applyOrderByDefaults;
exports.isWindowFunction = isWindowFunction;
exports.isRegularAggregation = isRegularAggregation;
exports.isColumnAggregated = isColumnAggregated;
exports.autoGenerateGroupBy = autoGenerateGroupBy;
exports.validateColumn = validateColumn;
exports.validateFilter = validateFilter;
/**
 * Generate a default alias for a column based on its configuration
 */
function generateColumnAlias(column) {
    if (column.alias) {
        return column.alias;
    }
    // For expressions, use a simplified version or fallback
    if (column.expr) {
        // Try to extract a meaningful name from simple expressions
        const simpleExprMatch = column.expr.match(/^(\w+)\s*\(/);
        if (simpleExprMatch) {
            return simpleExprMatch[1].toLowerCase();
        }
        return 'expr';
    }
    // For fields with aggregation
    if (column.field && column.agg) {
        const fieldName = getFieldBaseName(column.field);
        return `${column.agg}_${fieldName}`;
    }
    // For fields with date binning
    if (column.field && column.dateBin) {
        const fieldName = getFieldBaseName(column.field);
        return `${fieldName}_${column.dateBin}`;
    }
    // For fields with numeric binning
    if (column.field && column.binCount) {
        const fieldName = getFieldBaseName(column.field);
        return `${fieldName}_bin_${column.binCount}`;
    }
    // For raw fields
    if (column.field) {
        return getFieldBaseName(column.field);
    }
    // Fallback
    return 'column';
}
/**
 * Get the base name of a field (remove table prefix and clean up)
 */
function getFieldBaseName(field) {
    // Remove table prefix if present (e.g., "table.field" -> "field")
    const fieldName = field.includes('.') ? field.split('.').pop() : field;
    // Convert to snake_case and clean up
    return fieldName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');
}
/**
 * Apply default values to a filter
 */
function applyFilterDefaults(filter) {
    return {
        ...filter,
        type: filter.type || 'where',
        op: filter.op || (filter.field ? 'eq' : undefined)
    };
}
/**
 * Apply default values to an order by clause
 */
function applyOrderByDefaults(orderBy) {
    return {
        ...orderBy,
        direction: orderBy.direction || 'asc'
    };
}
/**
 * Check if an aggregation type is a window function
 */
function isWindowFunction(agg) {
    const windowFunctions = [
        'cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin',
        'moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min',
        'delta', 'pct_change', 'lag', 'lead',
        'rank', 'dense_rank', 'row_number', 'percent_rank', 'ntile'
    ];
    return windowFunctions.includes(agg);
}
/**
 * Check if an aggregation type is a regular aggregation
 */
function isRegularAggregation(agg) {
    const regularAggs = [
        'sum', 'avg', 'count', 'max', 'min', 'median', 'stddev', 'variance'
    ];
    return regularAggs.includes(agg);
}
/**
 * Check if a column is aggregated (either regular agg or window function)
 */
function isColumnAggregated(column) {
    return !!(column.agg && (isRegularAggregation(column.agg) || isWindowFunction(column.agg)));
}
/**
 * Auto-determine groupBy columns from non-aggregated columns
 */
function autoGenerateGroupBy(columns) {
    return columns
        .filter(col => !isColumnAggregated(col))
        .map(col => generateColumnAlias(col));
}
/**
 * Validate that required fields are present
 */
function validateColumn(column) {
    if (!column.field && !column.expr) {
        throw new Error('Column must have either field or expr');
    }
    if (column.field && column.expr) {
        throw new Error('Column cannot have both field and expr');
    }
}
/**
 * Validate that a filter has the required fields
 */
function validateFilter(filter) {
    if (!filter.field && !filter.expr) {
        throw new Error('Filter must have either field or expr');
    }
    if (filter.field && filter.expr) {
        throw new Error('Filter cannot have both field and expr');
    }
    if (filter.field && !filter.op) {
        throw new Error('Filter with field must have op');
    }
}
