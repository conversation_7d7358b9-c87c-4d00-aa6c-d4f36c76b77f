"use strict";
// New unified query builder implementation
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildQuery = buildQuery;
exports.buildQueryLegacy = buildQueryLegacy;
exports.cleanup = cleanup;
exports.buildQueryAuto = buildQueryAuto;
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
const defaults_1 = require("./utils/defaults");
const tableUtils_1 = require("./utils/tableUtils");
const processor_1 = require("./columns/processor");
const processor_2 = require("./filters/processor");
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
/**
 * Build a SQL query from the unified configuration
 */
function buildQuery(config) {
    // Validate basic configuration
    if (!config.table) {
        throw new Error('Table is required');
    }
    if (!config.columns || config.columns.length === 0) {
        throw new Error('At least one column is required');
    }
    // Process tables and joins
    const mainTable = (0, tableUtils_1.processTable)(config.table);
    const joins = (0, tableUtils_1.processJoins)(config.joins || [], mainTable, []);
    const allowedColumns = (0, tableUtils_1.getAllowedColumns)(mainTable, joins);
    // Process columns
    const processedColumns = (0, processor_1.processColumns)(config.columns, mainTable, joins, allowedColumns);
    // Determine if we need GROUP BY
    const needsGroupBy = (0, processor_1.hasAggregatedColumns)(processedColumns);
    const groupByColumns = needsGroupBy
        ? (config.groupBy || (0, processor_1.determineGroupByColumns)(processedColumns))
        : [];
    // Start building the query
    let query = knex.select(knex.raw((0, processor_1.generateSelectSQL)(processedColumns)));
    // Add FROM clause with joins
    const tableSQL = (0, tableUtils_1.generateTableSQL)(mainTable, joins);
    query = query.from(knex.raw(tableSQL));
    // Apply filters
    (0, processor_2.applyFilters)(query, config.filters || [], mainTable, joins, allowedColumns, needsGroupBy);
    // Add GROUP BY if needed
    if (needsGroupBy && groupByColumns.length > 0) {
        query = query.groupBy(groupByColumns.map(col => knex.ref(col)));
    }
    // Add ORDER BY
    if (config.orderBy && config.orderBy.length > 0) {
        config.orderBy.forEach(orderBy => {
            const processedOrderBy = (0, defaults_1.applyOrderByDefaults)(orderBy);
            query = query.orderBy(processedOrderBy.column, processedOrderBy.direction);
        });
    }
    // Add LIMIT and OFFSET
    if (config.limit) {
        query = query.limit(config.limit);
    }
    if (config.offset) {
        query = query.offset(config.offset);
    }
    return query;
}
/**
 * Legacy function for backward compatibility with old ChartConfig format
 */
function buildQueryLegacy(config) {
    // Convert old format to new format
    const newConfig = {
        table: config.table,
        columns: [],
        filters: config.filters,
        groupBy: config.groupBy,
        orderBy: config.orderBy,
        limit: config.limit,
        offset: config.offset
    };
    // Convert dimensions to columns
    if (config.dimensions) {
        config.dimensions.forEach((dim) => {
            if (typeof dim === 'string') {
                newConfig.columns.push({ field: dim });
            }
            else {
                newConfig.columns.push({
                    field: dim.field,
                    dateBin: dim.dateBin,
                    binCount: dim.binCount,
                    alias: dim.alias
                });
            }
        });
    }
    // Convert measures to columns
    if (config.measures) {
        config.measures.forEach((measure) => {
            if (measure.fn) {
                // Window function
                newConfig.columns.push({
                    field: measure.field,
                    agg: measure.fn,
                    partitionBy: measure.partitionBy,
                    orderBy: measure.orderBy,
                    frame: measure.frame,
                    alias: measure.alias
                });
            }
            else {
                // Regular aggregation
                newConfig.columns.push({
                    field: measure.field,
                    agg: measure.agg,
                    alias: measure.alias
                });
            }
        });
    }
    // Convert custom columns to columns
    if (config.customColumns) {
        config.customColumns.forEach((col) => {
            newConfig.columns.push({
                expr: col.expr,
                alias: col.alias
            });
        });
    }
    return buildQuery(newConfig);
}
/**
 * Cleanup function for database connections
 */
async function cleanup() {
    await knex.destroy();
}
// Export types for external use
__exportStar(require("./types"), exports);
// Main export that handles both new and legacy formats
function buildQueryAuto(config) {
    // Check if it's the new format (has 'columns' property)
    if ('columns' in config) {
        return buildQuery(config);
    }
    else {
        // Legacy format
        return buildQueryLegacy(config);
    }
}
