"use strict";
// Column processing and SQL generation
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processColumns = processColumns;
exports.determineGroupByColumns = determineGroupByColumns;
exports.hasAggregatedColumns = hasAggregatedColumns;
exports.hasWindowFunctions = hasWindowFunctions;
exports.generateSelectSQL = generateSelectSQL;
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
const defaults_1 = require("../utils/defaults");
const tableUtils_1 = require("../utils/tableUtils");
const parser_1 = require("../expressions/parser");
const dateBinning_1 = require("./dateBinning");
const numericBinning_1 = require("./numericBinning");
const aggregations_1 = require("./aggregations");
const windowFunctions_1 = require("./windowFunctions");
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
/**
 * Process all columns and generate their SQL expressions
 */
function processColumns(columns, mainTable, joins, allowedColumns) {
    const allTables = [mainTable, ...joins.map(j => j.table)];
    return columns.map(column => {
        (0, defaults_1.validateColumn)(column);
        const alias = (0, defaults_1.generateColumnAlias)(column);
        const isAggregated = (0, defaults_1.isColumnAggregated)(column);
        const isWindowFn = !!(column.agg && (0, defaults_1.isWindowFunction)(column.agg));
        // Create expression context
        const context = {
            allowedColumns,
            tables: allTables,
            hasGroupBy: false, // Will be set later
            processedColumns: [] // Will be populated as we process
        };
        const sqlExpression = generateColumnSQL(column, context, allTables);
        return {
            ...column,
            alias,
            isAggregated,
            isWindowFunction: isWindowFn,
            sqlExpression
        };
    });
}
/**
 * Generate SQL expression for a single column
 */
function generateColumnSQL(column, context, tables) {
    let baseExpression;
    if (column.expr) {
        // Handle Metabase expressions
        baseExpression = (0, parser_1.parseExpression)(column.expr, context);
    }
    else if (column.field) {
        // Handle raw fields
        baseExpression = processFieldReference(column.field, column.table, tables);
        // Apply date binning if specified
        if (column.dateBin) {
            baseExpression = (0, dateBinning_1.generateDateBinExpression)(baseExpression, column.dateBin);
        }
        // Apply numeric binning if specified
        if (column.binCount) {
            baseExpression = (0, numericBinning_1.generateNumericBinExpression)(baseExpression, column.binCount, column.field);
        }
    }
    else {
        throw new Error('Column must have either field or expr');
    }
    // Apply aggregation if specified
    if (column.agg) {
        if ((0, defaults_1.isWindowFunction)(column.agg)) {
            baseExpression = (0, windowFunctions_1.generateWindowFunctionSQL)(column.agg, baseExpression, column.partitionBy, column.orderBy, column.frame, context);
        }
        else if ((0, defaults_1.isRegularAggregation)(column.agg)) {
            baseExpression = (0, aggregations_1.generateAggregationSQL)(column.agg, baseExpression);
        }
    }
    return baseExpression;
}
/**
 * Process a field reference and qualify it with table if needed
 */
function processFieldReference(field, explicitTable, tables) {
    let qualifiedField;
    if (explicitTable) {
        // Use explicitly specified table
        const table = tables.find(t => t.qualifiedName === explicitTable || t.name === explicitTable);
        if (!table) {
            throw new Error(`Table '${explicitTable}' not found`);
        }
        qualifiedField = (0, tableUtils_1.qualifyFieldName)(field, table);
    }
    else if (field.includes('.')) {
        // Field is already qualified
        const tableName = (0, tableUtils_1.extractTableFromField)(field);
        const table = (0, tableUtils_1.findTableForField)(field, tables);
        if (!table) {
            throw new Error(`Table '${tableName}' not found`);
        }
        qualifiedField = field;
    }
    else {
        // Unqualified field - use as-is (could be enhanced to auto-detect table)
        qualifiedField = field;
    }
    // Quote the field name for SQL
    return knex.ref(qualifiedField).toSQL().sql;
}
/**
 * Determine which columns need to be in GROUP BY
 */
function determineGroupByColumns(processedColumns) {
    return processedColumns
        .filter(col => {
        // Include only non-aggregated columns that are not expressions
        // Raw fields and fields with date/numeric binning should be grouped
        return !col.isAggregated && col.field && !col.expr;
    })
        .map(col => col.alias);
}
/**
 * Check if any columns use aggregation
 */
function hasAggregatedColumns(processedColumns) {
    return processedColumns.some(col => col.isAggregated);
}
/**
 * Check if any columns use window functions
 */
function hasWindowFunctions(processedColumns) {
    return processedColumns.some(col => col.isWindowFunction);
}
/**
 * Generate SELECT clause SQL
 */
function generateSelectSQL(processedColumns) {
    return processedColumns
        .map(col => `${col.sqlExpression} AS "${col.alias}"`)
        .join(', ');
}
