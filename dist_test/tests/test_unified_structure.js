"use strict";
// Test the new unified query builder structure
Object.defineProperty(exports, "__esModule", { value: true });
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing New Unified Query Builder Structure ===\n');
(async () => {
    try {
        // Test 1: Simple query with auto-generated aliases
        console.log('1. Testing Simple Query with Auto-Generated Aliases:');
        const config1 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" }, // Auto-alias: sublayer
                { field: "Work done" }, // Auto-alias: work_done
                { field: "Date" } // Auto-alias: date
            ],
            limit: 3
        };
        const q1 = (0, queryBuilder_1.buildQuery)(config1);
        console.log('Generated SQL:', q1.toSQL().sql);
        const result1 = await q1;
        console.table(result1);
        console.log('✅ Simple query with auto-aliases working\n');
        // Test 2: Aggregation query with mixed column types
        console.log('2. Testing Aggregation Query with Mixed Column Types:');
        const config2 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" }, // Dimension
                { field: "Work done", agg: "sum", alias: "total_work" }, // Regular aggregation
                { field: "Scope", agg: "avg" }, // Auto-alias: avg_scope
                { expr: "count(*)", alias: "record_count" }, // Expression
                { field: "Date", agg: "count" } // Auto-alias: count_date
            ],
            filters: [
                { field: "Subactivity", op: "eq", value: "Piles Installed" }
            ],
            orderBy: [{ column: "total_work", direction: "desc" }],
            limit: 5
        };
        const q2 = (0, queryBuilder_1.buildQuery)(config2);
        console.log('Generated SQL:', q2.toSQL().sql);
        const result2 = await q2;
        console.table(result2);
        console.log('✅ Aggregation query with mixed columns working\n');
        // Test 3: Expression-based query with filters
        console.log('3. Testing Expression-Based Query with Filters:');
        const config3 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { field: "Work done", agg: "avg" } // Auto-alias: avg_work_done
            ],
            filters: [
                { expr: "year([Date]) = 2025", type: "where" },
                { expr: "avg([Work done]) > 100", type: "having" }
            ],
            limit: 5
        };
        const q3 = (0, queryBuilder_1.buildQuery)(config3);
        console.log('Generated SQL:', q3.toSQL().sql);
        const result3 = await q3;
        console.table(result3);
        console.log('✅ Expression-based query with filters working\n');
        // Test 4: Window functions with partitioning
        console.log('4. Testing Window Functions with Partitioning:');
        const config4 = {
            table: "progress_history",
            columns: [
                { field: "Sublayer" },
                { field: "Date" },
                { field: "Work done" },
                { field: "Work done", agg: "cumsum", partitionBy: ["Sublayer"], orderBy: "Date", alias: "cumulative_work" },
                { field: "Work done", agg: "moving_avg", orderBy: "Date", frame: { preceding: 7 }, alias: "moving_avg_7d" },
                { field: "Work done", agg: "rank", partitionBy: ["Sublayer"], orderBy: "Work done", alias: "work_rank" }
            ],
            filters: [
                { field: "Sublayer", op: "eq", value: "Block-2" }
            ],
            orderBy: [{ column: "Date" }],
            limit: 10
        };
        const q4 = (0, queryBuilder_1.buildQuery)(config4);
        console.log('Generated SQL:', q4.toSQL().sql);
        const result4 = await q4;
        console.table(result4);
        console.log('✅ Window functions with partitioning working\n');
        // Test 5: Date and numeric binning
        console.log('5. Testing Date and Numeric Binning:');
        const config5 = {
            table: "progress_history",
            columns: [
                { field: "Date", dateBin: "month" }, // Auto-alias: date_month
                { field: "Scope", binCount: 5 }, // Auto-alias: scope_bin_5
                { field: "Work done", agg: "sum" }, // Auto-alias: sum_work_done
                { field: "Date", agg: "count" } // Auto-alias: count_date
            ],
            orderBy: [{ column: "date_month" }],
            limit: 10
        };
        const q5 = (0, queryBuilder_1.buildQuery)(config5);
        console.log('Generated SQL:', q5.toSQL().sql);
        const result5 = await q5;
        console.table(result5);
        console.log('✅ Date and numeric binning working\n');
        console.log('🎉 All Unified Structure Tests Passed! 🎉');
        console.log('✅ Auto-generated aliases: Working');
        console.log('✅ Mixed column types: Working');
        console.log('✅ Expression filters: Working');
        console.log('✅ Window functions: Working');
        console.log('✅ Date/numeric binning: Working');
        console.log('✅ Unified aggregation types: Working');
    }
    catch (error) {
        console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
        console.error('Stack trace:', error instanceof Error ? error.stack : '');
        process.exit(1);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
