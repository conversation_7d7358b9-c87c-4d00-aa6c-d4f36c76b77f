"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test all date binning options with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing All Date Binning Options with Live Data ===\n');
const allDateBinOptions = [
    { bin: 'day', description: 'Calendar Day' },
    { bin: 'week', description: 'Calendar Week' },
    { bin: 'month', description: 'Calendar Month' },
    { bin: 'quarter', description: 'Calendar Quarter' },
    { bin: 'year', description: 'Calendar Year' },
    { bin: 'day_of_week', description: 'Day of Week (0=Sunday, 6=Saturday)' },
    { bin: 'month_of_year', description: 'Month of Year (1-12)' },
    { bin: 'quarter_of_year', description: 'Quarter of Year (1-4)' }
];
(async () => {
    try {
        for (const option of allDateBinOptions) {
            console.log(`\n--- ${option.description} ---`);
            const cfg = {
                table: "progress_history",
                columns: [
                    { field: "Date", dateBin: option.bin },
                    { field: "Work done", agg: "sum", alias: "total_work" },
                    { field: "Date", agg: "count", alias: "record_count" }
                ],
                filters: [
                    { field: "Subactivity", op: "eq", value: "Piles Installed" }
                ],
                orderBy: [
                    { column: `date_${option.bin}`, direction: "asc" }
                ],
                limit: 5
            };
            const q = (0, queryBuilder_1.buildQuery)(cfg);
            console.log(`SQL: ${q.toSQL().sql}`);
            const rows = await q;
            console.table(rows);
        }
        console.log('\n=== All Date Binning Options Test Complete ===');
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
