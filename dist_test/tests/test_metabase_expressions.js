"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test Metabase-compatible expressions with live data
const queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Testing Metabase-Compatible Expressions with Live Data ===\n');
// Helper function to validate expression results
function validateExpressionResult(result, testName, expectedChecks) {
    console.log(`\n--- Validating ${testName} ---`);
    if (result.length === 0) {
        console.log('❌ No results returned');
        return false;
    }
    let allValid = true;
    const firstRow = result[0];
    for (const [column, checkFn] of Object.entries(expectedChecks)) {
        if (!(column in firstRow)) {
            console.log(`❌ Column '${column}' not found in results`);
            allValid = false;
            continue;
        }
        const value = firstRow[column];
        const isValid = checkFn(value);
        console.log(`${isValid ? '✅' : '❌'} ${column}: ${value} ${isValid ? '(valid)' : '(invalid)'}`);
        if (!isValid)
            allValid = false;
    }
    return allValid;
}
(async () => {
    try {
        // Test 1: Math Functions
        console.log('1. Testing Math Functions with Live Data:');
        let cfg1 = {
            "table": "progress_history",
            "columns": [
                { "field": "Sublayer" },
                { "expr": "abs([Work done])", "alias": "abs_work" },
                { "expr": "round([Work done], 2)", "alias": "rounded_work" },
                { "expr": "ceil([Scope])", "alias": "ceiling_scope" },
                { "expr": "floor([Scope])", "alias": "floor_scope" },
                { "expr": "sqrt([Scope])", "alias": "sqrt_scope" },
                { "field": "Date", "agg": "count", "alias": "record_count" }
            ],
            "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
            "limit": 1
        };
        const q1 = (0, queryBuilder_1.buildQuery)(cfg1);
        console.log('Generated SQL:', q1.toSQL().sql);
        const result1 = await q1;
        console.table(result1);
        const isValid1 = validateExpressionResult(result1, "Math Functions", {
            "abs_work": (val) => (typeof val === 'string' || typeof val === 'number') && parseFloat(String(val)) >= 0,
            "rounded_work": (val) => (typeof val === 'string' || typeof val === 'number') && !isNaN(parseFloat(String(val))),
            "ceiling_scope": (val) => (typeof val === 'string' || typeof val === 'number') && Number.isInteger(parseFloat(String(val))),
            "floor_scope": (val) => (typeof val === 'string' || typeof val === 'number') && Number.isInteger(parseFloat(String(val))),
            "sqrt_scope": (val) => (typeof val === 'string' || typeof val === 'number') && parseFloat(String(val)) >= 0
        });
        if (!isValid1)
            throw new Error("Math functions test failed");
        // Test 2: String Functions
        console.log('\n2. Testing String Functions with Live Data:');
        let cfg2 = {
            "table": "progress_history",
            "columns": [
                { "field": "Date" },
                { "expr": "upper([Sublayer])", "alias": "upper_sublayer" },
                { "expr": "lower([Sublayer])", "alias": "lower_sublayer" },
                { "expr": "length([Sublayer])", "alias": "sublayer_length" },
                { "expr": "concat([Sublayer], \"-\", [Subactivity])", "alias": "combined_info" },
                { "field": "Date", "agg": "count", "alias": "record_count" }
            ],
            "limit": 1
        };
        const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
        console.log('Generated SQL:', q2.toSQL().sql);
        const result2 = await q2;
        console.table(result2);
        const isValid2 = validateExpressionResult(result2, "String Functions", {
            "upper_sublayer": (val) => typeof val === 'string' && val === val.toUpperCase(),
            "lower_sublayer": (val) => typeof val === 'string' && val === val.toLowerCase(),
            "sublayer_length": (val) => (typeof val === 'string' || typeof val === 'number') && parseInt(String(val)) > 0,
            "combined_info": (val) => typeof val === 'string' && val.includes('-')
        });
        if (!isValid2)
            throw new Error("String functions test failed");
        // Test 3: Date Functions
        console.log('\n3. Testing Date Functions with Live Data:');
        let cfg3 = {
            "table": "progress_history",
            "columns": [
                { "field": "Sublayer" },
                { "expr": "year([Date])", "alias": "year_extracted" },
                { "expr": "month([Date])", "alias": "month_extracted" },
                { "expr": "day([Date])", "alias": "day_extracted" },
                { "expr": "weekday([Date])", "alias": "weekday_extracted" },
                { "field": "Date", "agg": "count", "alias": "record_count" }
            ],
            "limit": 1
        };
        const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
        console.log('Generated SQL:', q3.toSQL().sql);
        const result3 = await q3;
        console.table(result3);
        const isValid3 = validateExpressionResult(result3, "Date Functions", {
            "year_extracted": (val) => (typeof val === 'string' || typeof val === 'number') && parseInt(String(val)) >= 2020 && parseInt(String(val)) <= 2030,
            "month_extracted": (val) => (typeof val === 'string' || typeof val === 'number') && parseInt(String(val)) >= 1 && parseInt(String(val)) <= 12,
            "day_extracted": (val) => (typeof val === 'string' || typeof val === 'number') && parseInt(String(val)) >= 1 && parseInt(String(val)) <= 31,
            "weekday_extracted": (val) => (typeof val === 'string' || typeof val === 'number') && parseInt(String(val)) >= 1 && parseInt(String(val)) <= 7
        });
        if (!isValid3)
            throw new Error("Date functions test failed");
        // Test 4: Logical Functions
        console.log('\n4. Testing Logical Functions with Live Data:');
        let cfg4 = {
            "table": "progress_history",
            "columns": [
                { "field": "Sublayer" },
                { "expr": "case([Work done] > 500, \"High\", [Work done] > 100, \"Medium\", \"Low\")", "alias": "work_category" },
                { "expr": "coalesce([Work done], 0)", "alias": "work_with_default" },
                { "expr": "isNull([Work done])", "alias": "is_work_null" },
                { "expr": "between([Scope], 1000, 5000)", "alias": "scope_in_range" },
                { "field": "Date", "agg": "count", "alias": "record_count" }
            ],
            "limit": 1
        };
        const q4 = (0, queryBuilder_1.buildQuery)(cfg4);
        console.log('Generated SQL:', q4.toSQL().sql);
        const result4 = await q4;
        console.table(result4);
        const isValid4 = validateExpressionResult(result4, "Logical Functions", {
            "work_category": (val) => typeof val === 'string' && ['High', 'Medium', 'Low'].includes(val),
            "work_with_default": (val) => (typeof val === 'string' || typeof val === 'number') && !isNaN(parseFloat(String(val))),
            "is_work_null": (val) => typeof val === 'boolean' || val === 'true' || val === 'false' || val === true || val === false,
            "scope_in_range": (val) => typeof val === 'boolean' || val === 'true' || val === 'false' || val === true || val === false
        });
        if (!isValid4)
            throw new Error("Logical functions test failed");
        // Test 5: Complex Nested Expressions (Most Important Test)
        console.log('\n5. Testing Complex Nested Expressions with Live Data:');
        let cfg5 = {
            "table": "progress_history",
            "columns": [
                { "field": "Sublayer" },
                { "expr": "round(abs([Work done] - [Scope]), 2)", "alias": "work_scope_diff" },
                { "expr": "case(upper([Sublayer]) = 'BLOCK-1', \"Primary\", \"Secondary\")", "alias": "block_priority" },
                { "expr": "concat(text(year([Date])), \"_\", text(month([Date])))", "alias": "year_month" },
                { "expr": "text([Scope])", "alias": "scope_as_text" },
                { "field": "Work done", "agg": "avg", "alias": "avg_work" }
            ],
            "limit": 1
        };
        const q5 = (0, queryBuilder_1.buildQuery)(cfg5);
        console.log('Generated SQL:', q5.toSQL().sql);
        const result5 = await q5;
        console.table(result5);
        const isValid5 = validateExpressionResult(result5, "Complex Nested Expressions", {
            "work_scope_diff": (val) => (typeof val === 'string' || typeof val === 'number') && !isNaN(parseFloat(String(val))) && parseFloat(String(val)) >= 0,
            "block_priority": (val) => typeof val === 'string' && ['Primary', 'Secondary'].includes(val),
            "year_month": (val) => typeof val === 'string' && !!val.match(/^\d{4}_\d{1,2}$/),
            "scope_as_text": (val) => typeof val === 'string' && !isNaN(parseFloat(val))
        });
        if (!isValid5)
            throw new Error("Complex nested expressions test failed");
        console.log('\n🎉 All Metabase Expression Tests Passed! 🎉');
        console.log('✅ Math Functions: Working');
        console.log('✅ String Functions: Working');
        console.log('✅ Date Functions: Working');
        console.log('✅ Logical Functions: Working');
        console.log('✅ Complex Nested Expressions: Working');
        console.log('\n=== Metabase Expressions Test Complete ===');
    }
    catch (error) {
        console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
    finally {
        await (0, queryBuilder_1.cleanup)();
        process.exit(0);
    }
})();
