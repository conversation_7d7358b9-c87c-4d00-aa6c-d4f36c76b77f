{"table": "orders", "dimensions": ["sale_date", "region"], "customColumns": [{"alias": "total_price", "expr": "quantity * unit_price"}], "measures": [{"agg": "sum", "field": "amount", "alias": "gross_sales"}, {"fn": "cumsum", "field": "amount", "orderBy": "sale_date", "alias": "run_total"}, {"fn": "moving_avg", "field": "amount", "orderBy": "sale_date", "frame": {"preceding": 6, "following": null}, "alias": "7day_avg"}, {"fn": "median", "field": "amount", "alias": "med_sale"}, {"fn": "percentile", "field": "amount", "percentile": 0.95, "alias": "p95_sale"}, {"fn": "pct_change", "field": "amount", "orderBy": "sale_date", "alias": "pct_diff"}, {"expr": "SUM(amount) OVER (PARTITION BY region ORDER BY sale_date ROWS BETWEEN 3 PRECEDING AND CURRENT ROW)", "alias": "rolling_sum_custom"}], "filters": [{"field": "sale_date", "op": "between", "value": ["2025-01-01", "2025-03-31"]}, {"field": "region", "op": "in", "value": ["UAE", "KSA"]}, {"field": "run_total", "op": "gt", "value": 100000, "having": true}], "orderBy": [{"field": "sale_date", "direction": "asc"}], "limit": 2000, "offset": 0}