// Working Complex Example - Adapted to Actual Database Schema
import { buildQuery, cleanup } from '../src/queryBuilder';

async function runWorkingComplexExample() {
    console.log('🎯 === WORKING COMPLEX PROGRESS ANALYSIS === 🎯\n');

    // Example 1: Hierarchical Progress Analysis with Percentages
    console.log('📊 1. Hierarchical Progress Analysis:');
    console.log('   ✅ Layer, Sublayer, Activity, Subactivity, Week grouping');
    console.log('   ✅ Percentage of work done vs scope');
    console.log('   ✅ Multiple cumulative sum patterns\n');

    const hierarchicalConfig = {
        table: "progress_history",
        columns: [
            // Hierarchical grouping
            { field: "Layer" },
            { field: "Sublayer" },
            { field: "Activity" },
            { field: "Subactivity" },
            { field: "Date", dateBin: "week" as const, alias: "week" },
            
            // Work metrics
            { field: "Work done", agg: "sum" as const, alias: "work_done_this_week" },
            { field: "Scope", agg: "sum" as const, alias: "total_scope" },
            
            // Percentage calculation
            { expr: "round((sum([Work done]) * 100.0 / nullif(sum([Scope]), 0)), 2)", alias: "completion_percentage" },
            
            // Cumulative sums at different hierarchy levels
            { 
                field: "Work done", 
                agg: "cumsum" as const, 
                partitionBy: ["Layer"], 
                orderBy: "week", 
                alias: "cumulative_by_layer" 
            },
            { 
                field: "Work done", 
                agg: "cumsum" as const, 
                partitionBy: ["Layer", "Sublayer"], 
                orderBy: "week", 
                alias: "cumulative_by_sublayer" 
            },
            { 
                field: "Work done", 
                agg: "cumsum" as const, 
                partitionBy: ["Layer", "Sublayer", "Activity"], 
                orderBy: "week", 
                alias: "cumulative_by_activity" 
            },
            { 
                field: "Work done", 
                agg: "cumsum" as const, 
                orderBy: "week", 
                alias: "global_cumulative" 
            }
        ],
        filters: [
            { expr: 'EXTRACT(YEAR FROM "Date") = 2025' },
            { field: "Work done", op: "gt" as const, value: 0 }
        ],
        orderBy: [
            { column: "Layer", direction: "asc" as const },
            { column: "Sublayer", direction: "asc" as const },
            { column: "week", direction: "asc" as const }
        ],
        limit: 20
    };

    const q1 = buildQuery(hierarchicalConfig);
    console.log('Generated SQL:');
    console.log(q1.toSQL().sql);
    console.log('\n');

    try {
        const results1 = await q1;
        console.log('📈 Hierarchical Analysis Results:');
        console.table(results1.slice(0, 8));
        console.log(`✅ Retrieved ${results1.length} hierarchical records\n`);
    } catch (error) {
        console.error('❌ Hierarchical query failed:', error instanceof Error ? error.message : String(error));
        console.log('\n');
    }

    // Example 2: JOIN with Progress Data
    console.log('🔗 2. JOIN Analysis with Progress Data:');
    console.log('   ✅ JOIN progress_history with progress_data');
    console.log('   ✅ Extract planned vs actual metrics');
    console.log('   ✅ Cross-table cumulative calculations\n');

    const joinConfig = {
        table: { name: "progress_history", alias: "ph" },
        joins: [
            {
                table: { name: "progress_data", alias: "pd" },
                type: "inner" as const,
                on: { left: "ph.Sublayer", right: "pd.Sublayer" }
            }
        ],
        columns: [
            { field: "ph.Layer", alias: "layer" },
            { field: "ph.Sublayer", alias: "sublayer" },
            { field: "ph.Activity", alias: "activity" },
            { field: "ph.Subactivity", alias: "subactivity" },
            { field: "ph.Date", dateBin: "week" as const, alias: "week" },
            
            // Actual vs Planned metrics
            { field: "ph.Work done", agg: "sum" as const, alias: "actual_work" },
            { field: "ph.Scope", agg: "sum" as const, alias: "actual_scope" },
            { field: "pd.Scope", agg: "sum" as const, alias: "planned_scope" },
            
            // Percentage calculations
            { expr: "round((sum([ph.Work done]) * 100.0 / nullif(sum([ph.Scope]), 0)), 2)", alias: "actual_completion_pct" },
            { expr: "round((sum([ph.Work done]) * 100.0 / nullif(sum([pd.Scope]), 0)), 2)", alias: "vs_planned_pct" },
            
            // Cross-table cumulative sums
            { 
                field: "ph.Work done", 
                agg: "cumsum" as const, 
                partitionBy: ["ph.Sublayer"], 
                orderBy: "week", 
                alias: "cumulative_actual_work" 
            },
            { 
                field: "pd.Scope", 
                agg: "cumsum" as const, 
                partitionBy: ["ph.Sublayer"], 
                orderBy: "week", 
                alias: "cumulative_planned_scope" 
            },
            
            // Variance analysis
            { expr: "sum([ph.Work done]) - sum([pd.Scope])", alias: "work_vs_plan_variance" },
            { expr: "round((sum([ph.Work done]) / nullif(sum([pd.Scope]), 0)), 4)", alias: "efficiency_ratio" }
        ],
        filters: [
            { field: "ph.Subactivity", op: "eq" as const, value: "Piles Installed" },
            { expr: 'EXTRACT(YEAR FROM "ph"."Date") = 2025' }
        ],
        orderBy: [
            { column: "layer", direction: "asc" as const },
            { column: "sublayer", direction: "asc" as const },
            { column: "week", direction: "asc" as const }
        ],
        limit: 25
    };

    const q2 = buildQuery(joinConfig);
    console.log('Generated SQL:');
    console.log(q2.toSQL().sql);
    console.log('\n');

    try {
        const results2 = await q2;
        console.log('🔗 JOIN Analysis Results:');
        console.table(results2.slice(0, 8));
        console.log(`✅ Retrieved ${results2.length} joined records\n`);
        
        if (results2.length > 0) {
            console.log('📊 Key Insights:');
            const avgEfficiency = results2.reduce((sum, row) => sum + parseFloat(row.efficiency_ratio || '0'), 0) / results2.length;
            const totalVariance = results2.reduce((sum, row) => sum + parseFloat(row.work_vs_plan_variance || '0'), 0);
            console.log(`   📈 Average Efficiency Ratio: ${avgEfficiency.toFixed(4)}`);
            console.log(`   📊 Total Work vs Plan Variance: ${totalVariance.toFixed(2)}`);
        }
    } catch (error) {
        console.error('❌ JOIN query failed:', error instanceof Error ? error.message : String(error));
        console.log('\n');
    }

    // Example 3: Advanced Analytics with Rolling Windows
    console.log('📊 3. Advanced Analytics with Rolling Windows:');
    console.log('   ✅ Rolling averages and trends');
    console.log('   ✅ Performance rankings');
    console.log('   ✅ Week-over-week analysis\n');

    const advancedConfig = {
        table: "progress_history",
        columns: [
            { field: "Layer" },
            { field: "Sublayer" },
            { field: "Date", dateBin: "week" as const, alias: "week" },
            
            // Current metrics
            { field: "Work done", agg: "sum" as const, alias: "weekly_work" },
            { field: "Scope", agg: "sum" as const, alias: "weekly_scope" },
            
            // Rolling 4-week average
            { 
                field: "Work done", 
                agg: "avg" as const,
                frame: { preceding: 3, following: 0 },
                orderBy: "week",
                partitionBy: ["Sublayer"],
                alias: "rolling_4week_avg" 
            },
            
            // Cumulative totals
            { 
                field: "Work done", 
                agg: "cumsum" as const, 
                partitionBy: ["Sublayer"], 
                orderBy: "week", 
                alias: "cumulative_work" 
            },
            
            // Performance ranking within each week
            { 
                field: "Work done", 
                agg: "rank" as const, 
                orderBy: "weekly_work", 
                partitionBy: ["week"],
                alias: "weekly_rank" 
            },
            
            // Efficiency metrics
            { expr: "round((sum([Work done]) / nullif(sum([Scope]), 0)), 4)", alias: "efficiency" }
        ],
        filters: [
            { field: "Subactivity", op: "eq" as const, value: "Piles Installed" },
            { field: "Work done", op: "gt" as const, value: 0 }
        ],
        orderBy: [
            { column: "Sublayer", direction: "asc" as const },
            { column: "week", direction: "asc" as const }
        ],
        limit: 30
    };

    const q3 = buildQuery(advancedConfig);
    console.log('Generated SQL:');
    console.log(q3.toSQL().sql);
    console.log('\n');

    try {
        const results3 = await q3;
        console.log('📈 Advanced Analytics Results:');
        console.table(results3.slice(0, 10));
        console.log(`✅ Retrieved ${results3.length} advanced analytics records\n`);
    } catch (error) {
        console.error('❌ Advanced analytics query failed:', error instanceof Error ? error.message : String(error));
        console.log('\n');
    }

    console.log('🎉 === COMPLEX ANALYSIS COMPLETE === 🎉');
    console.log('\n📋 Successfully Demonstrated:');
    console.log('✅ Hierarchical grouping (Layer → Sublayer → Activity → Subactivity → Week)');
    console.log('✅ Percentage calculations (work done vs total scope)');
    console.log('✅ Multiple cumulative sum patterns with different partitioning');
    console.log('✅ Complex JOINs with cross-table analysis');
    console.log('✅ Advanced window functions (rolling averages, rankings)');
    console.log('✅ Variance analysis and efficiency metrics');
    console.log('✅ Real-time data processing with proper filtering');
}

// Run the working complex example
runWorkingComplexExample()
    .then(() => cleanup())
    .then(() => {
        console.log('\n✅ Working complex example completed successfully!');
        process.exit(0);
    })
    .catch(error => {
        console.error('\n❌ Working complex example failed:', error);
        process.exit(1);
    });
