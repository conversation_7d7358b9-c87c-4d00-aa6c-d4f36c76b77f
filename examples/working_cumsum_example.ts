// Working examples of cumsum with Scope
import { buildQuery, ChartConfig } from '../src/queryBuilder';

console.log('=== Working Cumsum Examples ===\n');

// Example 1: Cumsum without GROUP BY (no other measures)
console.log('1. Cumsum of Scope without GROUP BY:');
let cfg1 = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],  // These become SELECT columns, not GROUP BY
    "measures": [
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        }
        // No aggregation measures = No GROUP BY
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" as const },
        { "field": "Sublayer", "direction": "asc" as const }
    ],
    "limit": 15
};

// Example 2: Cumsum with date binning (includes Date in GROUP BY)
console.log('\n2. Cumsum with monthly aggregation:');
let cfg2 = {
    "table": "progress_history",
    "dimensions": [
        { "field": "Date", "dateBin": "month" },  // This creates Date_month in GROUP BY
        "Sublayer"
    ],
    "measures": [
        {
            "agg": "sum",
            "field": "Scope",
            "alias": "monthly_scope"
        },
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date_month",  // Order by the grouped column
            "alias": "running_monthly_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date_month", "direction": "asc" as const },
        { "field": "Sublayer", "direction": "asc" as const }
    ],
    "limit": 15
};

// Example 3: Simple cumsum by Sublayer (order by Sublayer)
console.log('\n3. Cumsum ordered by Sublayer:');
let cfg3 = {
    "table": "progress_history",
    "dimensions": ["Sublayer"],
    "measures": [
        {
            "agg": "sum",
            "field": "Scope",
            "alias": "total_scope"
        },
        {
            "fn": "cumsum",
            "field": "Scope",
            "orderBy": "Sublayer",  // Order by the grouped column
            "alias": "running_scope_by_sublayer"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Sublayer", "direction": "asc" as const }
    ],
    "limit": 10
};

(async () => {
    try {
        console.log('\n--- Example 1: No GROUP BY ---');
        const q1 = buildQuery(cfg1 as ChartConfig);
        console.log('SQL:', q1.toSQL().sql);
        const rows1 = await q1;
        console.table(rows1);

        console.log('\n--- Example 2: Monthly Aggregation ---');
        const q2 = buildQuery(cfg2 as ChartConfig);
        console.log('SQL:', q2.toSQL().sql);
        const rows2 = await q2;
        console.table(rows2);

        console.log('\n--- Example 3: By Sublayer ---');
        const q3 = buildQuery(cfg3 as ChartConfig);
        console.log('SQL:', q3.toSQL().sql);
        const rows3 = await q3;
        console.table(rows3);

    } catch (error) {
        console.error('Error:', error);
    }
})();
