"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Comprehensive examples of date binning functionality
var queryBuilder_1 = require("./queryBuilder");
console.log('=== Date Binning Examples ===\n');
// All supported date binning options
var examples = [
    {
        name: "Day Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day" }],
            measures: [{ agg: "sum", field: "Work done", alias: "daily_work" }],
            limit: 3
        }
    },
    {
        name: "Week Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "week" }],
            measures: [{ agg: "sum", field: "Work done", alias: "weekly_work" }],
            limit: 3
        }
    },
    {
        name: "Month Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "month" }],
            measures: [{ agg: "sum", field: "Work done", alias: "monthly_work" }],
            limit: 3
        }
    },
    {
        name: "Quarter Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "quarter" }],
            measures: [{ agg: "sum", field: "Work done", alias: "quarterly_work" }],
            limit: 3
        }
    },
    {
        name: "Year Binning",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "yearly_work" }],
            limit: 3
        }
    },
    {
        name: "Day of Week (0=Sunday, 6=Saturday)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_week" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_weekday" }],
            limit: 7
        }
    },
    {
        name: "Day of Month (1-31)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "day_of_month" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_day" }],
            limit: 5
        }
    },
    {
        name: "Month of Year (1-12)",
        config: {
            table: "progress_history",
            dimensions: [{ field: "Date", dateBin: "month_of_year" }],
            measures: [{ agg: "sum", field: "Work done", alias: "work_by_month" }],
            limit: 12
        }
    },
    {
        name: "Custom Alias Example",
        config: {
            table: "progress_history",
            dimensions: [
                { field: "Date", dateBin: "quarter", alias: "reporting_quarter" },
                "Sublayer"
            ],
            measures: [{ agg: "sum", field: "Scope", alias: "total_scope" }],
            orderBy: [{ field: "reporting_quarter", direction: "asc" }],
            limit: 5
        }
    }
];
// Generate SQL for each example
examples.forEach(function (example, index) {
    console.log("".concat(index + 1, ". ").concat(example.name, ":"));
    var q = (0, queryBuilder_1.buildQuery)(example.config);
    console.log("   SQL: ".concat(q.toSQL().sql));
    console.log('');
});
console.log('=== Usage Notes ===');
console.log('• Use the old string format: "Date" for raw date columns');
console.log('• Use the new object format: { field: "Date", dateBin: "month" } for date binning');
console.log('• Generated aliases follow the pattern: fieldName_dateBin (e.g., "Date_month")');
console.log('• You can override the alias: { field: "Date", dateBin: "month", alias: "my_month" }');
console.log('• Mix old and new formats: ["Date", { field: "Date", dateBin: "month" }, "Sublayer"]');
console.log('• Use "dont_bin" to explicitly disable binning while using object syntax');
