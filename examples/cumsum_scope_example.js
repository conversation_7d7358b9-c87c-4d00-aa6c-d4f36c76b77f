"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Example showing cumsum of Scope with different scenarios
var queryBuilder_1 = require("../src/queryBuilder");
console.log('=== Cumsum of Scope Examples ===\n');
// Example 1: Simple cumsum without date binning
console.log('1. Simple cumsum of Scope by Sublayer:');
var cfg1 = {
    "table": "progress_history",
    "dimensions": ["Sublayer"],
    "measures": [
        {
            "agg": "sum",
            "field": "Scope",
            "alias": "total_scope"
        },
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 10
};
// Example 2: Cumsum with raw date dimension (no binning)
console.log('\n2. Cumsum of Scope with Date dimension (no binning):');
var cfg2 = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],
    "measures": [
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" },
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 10
};
// Example 3: Cumsum across all data (no partition)
console.log('\n3. Global cumsum of Scope (no partition):');
var cfg3 = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],
    "measures": [
        {
            "agg": "sum",
            "field": "Scope",
            "alias": "scope"
        },
        {
            "fn": "cumsum",
            "field": "Scope",
            "orderBy": "Date",
            "alias": "global_running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" }
    ],
    "limit": 10
};
(function () { return __awaiter(void 0, void 0, void 0, function () {
    var q1, rows1, q2, rows2, q3, rows3, error_1;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 4, , 5]);
                console.log('\n--- Example 1 Results ---');
                q1 = (0, queryBuilder_1.buildQuery)(cfg1);
                console.log('SQL:', q1.toSQL().sql);
                return [4 /*yield*/, q1];
            case 1:
                rows1 = _a.sent();
                console.table(rows1);
                console.log('\n--- Example 2 Results ---');
                q2 = (0, queryBuilder_1.buildQuery)(cfg2);
                console.log('SQL:', q2.toSQL().sql);
                return [4 /*yield*/, q2];
            case 2:
                rows2 = _a.sent();
                console.table(rows2);
                console.log('\n--- Example 3 Results ---');
                q3 = (0, queryBuilder_1.buildQuery)(cfg3);
                console.log('SQL:', q3.toSQL().sql);
                return [4 /*yield*/, q3];
            case 3:
                rows3 = _a.sent();
                console.table(rows3);
                return [3 /*break*/, 5];
            case 4:
                error_1 = _a.sent();
                console.error('Error:', error_1);
                return [3 /*break*/, 5];
            case 5: return [2 /*return*/];
        }
    });
}); })();
