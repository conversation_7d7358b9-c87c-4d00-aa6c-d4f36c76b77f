// demo.ts
import { buildQuery, ChartConfig } from '../src/queryBuilder';
// import cfg from './config.json';

// Example showing date binning functionality
let cfg = {
    "table": "progress_history",
    "dimensions": [
        // New date binning syntax - group by month
        { "field": "Date", "dateBin": "month" },
        // Mix with regular string dimensions
        "Sublayer"
    ],
    "measures": [
        {
            "agg": "sum",
            "field": "Work done",
            "alias": "monthly_work_done"
        },
        {
            "agg": "sum",
            "field": "Scope",
            "alias": "monthly_scope"
        },
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date_month",  // Use the binned date alias for ordering
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date_month", "direction": "asc" },  // Note: use the generated alias
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 20
};


(async () => {
    const q = buildQuery(cfg as ChartConfig);
    console.log('Generated SQL:');
    console.log(q.toSQL().toNative());  // see generated SQL & bindings
    console.log('\nSQL formatted:');
    console.log(q.toSQL().sql);
    console.log('\nExecuting query...');
    const rows = await q;              // execute & get rows
    console.table(rows);
})();
