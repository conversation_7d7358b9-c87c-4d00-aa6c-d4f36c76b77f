// main.ts - Updated for New Unified Query Builder Structure
import { buildQuery, cleanup } from '../src/queryBuilder';

// Example showing the new unified structure with date binning and window functions
let cfg = {
    "table": "progress_history",
    "columns": [
        // Date binning with auto-generated alias (date_month)
        { "field": "Date", "dateBin": "month" as const, "alias": "month" },
        // Regular field
        { "field": "Sublayer" },
        // Aggregation
        {
            "field": "Work done",
            "agg": "sum" as const,
            "alias": "monthly_work_done"
        },
        // Window function with partitioning
        {
            "field": "Scope",
            "agg": "cumsum" as const,
            "partitionBy": ["Sublayer", "month"],  // Partition by both Sublayer and month
            "orderBy": "month",  // Order by month within each partition
            "alias": "running_scope"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq" as const, "value": "Piles Installed" }
    ],
    "orderBy": [
        { "column": "month", "direction": "asc" as const },  // Use column instead of field
        { "column": "sublayer", "direction": "asc" as const }  // Auto-generated alias
    ],
    "limit": 20
};


(async () => {
    try {
        const q = buildQuery(cfg);
        console.log('Generated SQL:');
        console.log(q.toSQL().toNative());  // see generated SQL & bindings
        console.log('\nSQL formatted:');
        console.log(q.toSQL().sql);
        console.log('\nExecuting query...');
        const rows = await q;              // execute & get rows
        console.table(rows);
    } catch (error) {
        console.error('Error:', error);
    } finally {
        // Close the database connection to allow the process to exit
        await cleanup();
        process.exit(0);
    }
})();
