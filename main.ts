// demo.ts
import { buildQuery, ChartConfig } from './queryBuilder';
// import cfg from './config.json';

let cfg = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],
    "measures": [
        {
            "agg": "sum",
            "field": "Work done",
            "alias": "work_done"
        },
        {
            "fn": "cumsum",
            "field": "Work done",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_work_done"
        }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" },
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 10000
};


(async () => {
    const q = buildQuery(cfg as ChartConfig);
    console.log('Generated SQL:');
    console.log(q.toSQL().toNative());  // see generated SQL & bindings
    console.log('\nSQL formatted:');
    console.log(q.toSQL().sql);
    console.log('\nExecuting query...');
    const rows = await q;              // execute & get rows
    console.table(rows);
})();
