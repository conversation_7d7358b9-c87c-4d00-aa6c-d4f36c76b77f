// demo.ts
import { buildQuery, ChartConfig } from './queryBuilder';
// import cfg from './config.json';

let cfg = {
    "table": "progress_history",
    "dimensions": ["Date", "Sublayer"],
    "measures": [
        {
            "agg": "sum",
            "field": "Work done",
            "alias": "work_done"
        },
        // {
        //     "fn": "cumsum",
        //     "field": "Work done",
        //     "partitionBy": ["Sublayer"],
        //     "orderBy": "Date",
        //     "alias": "running_work_done"
        // },
        {
            "fn": "cumsum",
            "field": "Scope",
            "partitionBy": ["Sublayer"],
            "orderBy": "Date",
            "alias": "running_scope"
        },
        // {
        //     "fn": "moving_avg",
        //     "field": "Work done",
        //     "orderBy": "Date",
        //     "frame": { "preceding": 2, "following": null },
        //     "alias": "moving_avg_work"
        // },
        // {
        //     "fn": "delta",
        //     "field": "Work done",
        //     "orderBy": "Date",
        //     "alias": "work_delta"
        // },
        // {
        //     "fn": "pct_change",
        //     "field": "Work done",
        //     "orderBy": "Date",
        //     "alias": "work_pct_change"
        // }
    ],
    "filters": [
        { "field": "Subactivity", "op": "eq", "value": "Piles Installed" }
    ],
    "orderBy": [
        { "field": "Date", "direction": "asc" },
        { "field": "Sublayer", "direction": "asc" }
    ],
    "limit": 10
};


(async () => {
    const q = buildQuery(cfg as ChartConfig);
    console.log('Generated SQL:');
    console.log(q.toSQL().toNative());  // see generated SQL & bindings
    console.log('\nSQL formatted:');
    console.log(q.toSQL().sql);
    console.log('\nExecuting query...');
    const rows = await q;              // execute & get rows
    console.table(rows);
})();
