# Proposed Clean Query Builder Config Structure

## Core Principle: Everything is a Column
Whether it's a raw field, calculated expression, or aggregation - they're all just columns with different behaviors.

## Simple Unified Structure

```typescript
interface QueryConfig {
  table: string;
  
  // All columns (raw fields, expressions, aggregations)
  columns: Column[];
  
  // Filters (traditional or expression-based)
  filters?: Filter[];
  
  // Grouping, ordering, pagination
  groupBy?: string[];  // Column aliases to group by
  orderBy?: { column: string; direction: 'asc' | 'desc' }[];
  limit?: number;
  offset?: number;
}

interface Column {
  alias: string;                    // Always required - what to call this column
  
  // Option 1: Raw field
  field?: string;                   // "Work done", "Date", etc.
  
  // Option 2: Expression (Metabase-style)
  expr?: string;                    // "abs([Work done] - [Scope])", "year([Date])", etc.
  
  // Option 3: Aggregation
  agg?: 'sum' | 'avg' | 'count' | 'max' | 'min';  // Applied to field or expr
  
  // Option 4: Window function
  window?: 'cumsum' | 'moving_avg' | 'delta' | 'rank';
  partitionBy?: string[];
  orderBy?: string;
  
  // Special behaviors
  dateBin?: 'day' | 'week' | 'month' | 'quarter' | 'year';  // For date fields
  binCount?: number;                // For numeric histograms
}

interface Filter {
  // Option 1: Traditional field filter
  field?: string;
  op?: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like';
  value?: any;
  
  // Option 2: Expression filter
  expr?: string;                    // "abs([Work done] - [Scope]) > 1000"
  
  having?: boolean;                 // Apply after GROUP BY
}
```

## Examples

### 1. Simple Query (Raw Fields)
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },
    { alias: "work_done", field: "Work done" },
    { alias: "date", field: "Date" }
  ],
  limit: 10
}
```

### 2. Aggregation Query
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },                    // Dimension
    { alias: "total_work", field: "Work done", agg: "sum" },     // Measure
    { alias: "avg_scope", field: "Scope", agg: "avg" },         // Measure
    { alias: "record_count", field: "Date", agg: "count" }      // Measure
  ],
  groupBy: ["sublayer"],
  orderBy: [{ column: "total_work", direction: "desc" }],
  limit: 5
}
```

### 3. Expression-Based Query
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },
    { alias: "work_category", expr: "case([Work done] > 500, 'High', 'Low')" },
    { alias: "work_scope_diff", expr: "abs([Work done] - [Scope])" },
    { alias: "year_month", expr: "concat(text(year([Date])), '-', text(month([Date])))" },
    { alias: "avg_work", field: "Work done", agg: "avg" }
  ],
  filters: [
    { expr: "year([Date]) = 2025" },
    { field: "Subactivity", op: "eq", value: "Piles Installed" }
  ],
  groupBy: ["sublayer", "work_category", "work_scope_diff", "year_month"],
  limit: 10
}
```

### 4. Advanced Query (Window Functions + Date Binning)
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "month", field: "Date", dateBin: "month" },         // Date binning
    { alias: "scope_bucket", field: "Scope", binCount: 10 },     // Numeric binning
    { alias: "total_work", field: "Work done", agg: "sum" },     // Aggregation
    { alias: "cumulative_work", field: "Work done", agg: "sum", window: "cumsum", orderBy: "Date" }, // Window function
    { alias: "work_trend", expr: "round([Work done] / [Scope] * 100, 2)" }  // Expression
  ],
  filters: [
    { expr: "year([Date]) >= 2024" }
  ],
  groupBy: ["month", "scope_bucket"],
  orderBy: [{ column: "month", direction: "asc" }],
  limit: 50
}
```

## Benefits of This Structure

1. **Unified**: Everything is a column - no artificial separation
2. **Flexible**: Same column can be raw field, expression, or aggregation
3. **Composable**: Mix and match different column types freely
4. **Intuitive**: Clear what each column represents
5. **Extensible**: Easy to add new column behaviors
6. **Simple**: One concept to learn instead of multiple

## Migration Path

The current structure can be automatically converted:
- `dimensions` → `columns` with just `field` or `expr`
- `customColumns` → `columns` with `expr`
- `measures` → `columns` with `agg` or `window`
- `filters` remain the same

This gives us the best of both worlds - simplicity and power!
