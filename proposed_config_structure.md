# Proposed Clean Query Builder Config Structure

## Core Principle: Everything is a Column
Whether it's a raw field, calculated expression, or aggregation - they're all just columns with different behaviors.

## Simple Unified Structure

```typescript
interface QueryConfig {
  // Tables and joins
  table: string | TableRef;         // Main table (can be string or object with alias)
  joins?: Join[];                   // Additional tables to join

  // All columns (raw fields, expressions, aggregations)
  columns: Column[];

  // Filters (traditional or expression-based)
  filters?: Filter[];

  // Grouping, ordering, pagination
  groupBy?: string[];  // Column aliases to group by
  orderBy?: { column: string; direction: 'asc' | 'desc' }[];
  limit?: number;
  offset?: number;
}

interface TableRef {
  name: string;                     // Table name
  alias?: string;                   // Table alias
}

interface Join {
  table: string | TableRef;         // Table to join
  type?: 'inner' | 'left' | 'right' | 'full';  // Join type (default: inner)

  // Join condition - Option 1: Simple field equality
  on?: {
    left: string;                   // Field from left table (table.field or just field)
    right: string;                  // Field from right table (table.field or just field)
  };

  // Join condition - Option 2: Complex expression
  condition?: string;               // Full join condition expression
}

interface Column {
  alias: string;                    // Always required - what to call this column

  // Option 1: Raw field
  field?: string;                   // "Work done", "Date", "table.field", etc.
  table?: string;                   // Table name/alias (optional if unambiguous)

  // Option 2: Expression (Metabase-style)
  expr?: string;                    // "abs([Work done] - [Scope])", "year([Date])", etc.

  // Option 3: Aggregation (includes regular agg + window functions)
  agg?: 'sum' | 'avg' | 'count' | 'max' | 'min' | 'median' |
        'cumsum' | 'cumavg' | 'cumcount' | 'cummax' | 'cummin' |
        'moving_avg' | 'moving_sum' | 'moving_count' |
        'delta' | 'pct_change' | 'lag' | 'lead' |
        'rank' | 'dense_rank' | 'row_number' | 'percent_rank';

  // Window function controls (only used with window agg functions)
  partitionBy?: string[];           // PARTITION BY columns
  orderBy?: string | string[];      // ORDER BY columns
  frame?: {                         // Window frame (for moving functions)
    preceding?: number;             // ROWS n PRECEDING
    following?: number;             // ROWS n FOLLOWING
  };

  // Special behaviors
  dateBin?: 'day' | 'week' | 'month' | 'quarter' | 'year';  // For date fields
  binCount?: number;                // For numeric histograms
}

interface Filter {
  // Option 1: Traditional field filter
  field?: string;
  op?: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like';
  value?: any;

  // Option 2: Expression filter
  expr?: string;                    // "abs([Work done] - [Scope]) > 1000"

  // Filter placement
  type?: 'where' | 'having' | 'qualify';  // WHERE (default), HAVING (post-GROUP BY), QUALIFY (post-window)
}
```

## Examples

### 1. Simple Query (Raw Fields)
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },
    { alias: "work_done", field: "Work done" },
    { alias: "date", field: "Date" }
  ],
  limit: 10
}
```

### 2. Aggregation Query
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },                    // Dimension
    { alias: "total_work", field: "Work done", agg: "sum" },     // Regular aggregation
    { alias: "avg_scope", field: "Scope", agg: "avg" },         // Regular aggregation
    { alias: "record_count", field: "Date", agg: "count" }      // Regular aggregation
  ],
  groupBy: ["sublayer"],
  orderBy: [{ column: "total_work", direction: "desc" }],
  limit: 5
}
```

### 3. Expression-Based Query
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },
    { alias: "work_category", expr: "case([Work done] > 500, 'High', 'Low')" },
    { alias: "work_scope_diff", expr: "abs([Work done] - [Scope])" },
    { alias: "year_month", expr: "concat(text(year([Date])), '-', text(month([Date])))" },
    { alias: "avg_work", field: "Work done", agg: "avg" }
  ],
  filters: [
    { expr: "year([Date]) = 2025" },
    { field: "Subactivity", op: "eq", value: "Piles Installed" }
  ],
  groupBy: ["sublayer", "work_category", "work_scope_diff", "year_month"],
  limit: 10
}
```

### 4. Advanced Query (Window Functions + All Filter Types)
```typescript
{
  table: "progress_history",
  columns: [
    { alias: "sublayer", field: "Sublayer" },
    { alias: "month", field: "Date", dateBin: "month" },
    { alias: "total_work", field: "Work done", agg: "sum" },                    // Regular agg
    { alias: "cumulative_work", field: "Work done", agg: "cumsum",
      partitionBy: ["Sublayer"], orderBy: "Date" },                           // Window function
    { alias: "moving_avg", field: "Work done", agg: "moving_avg",
      orderBy: "Date", frame: { preceding: 7 } },                             // Moving window
    { alias: "work_rank", field: "Work done", agg: "rank",
      partitionBy: ["Sublayer"], orderBy: "Work done" },                      // Ranking
    { alias: "prev_work", field: "Work done", agg: "lag",
      partitionBy: ["Sublayer"], orderBy: "Date" }                            // Offset
  ],
  filters: [
    { expr: "year([Date]) >= 2024", type: "where" },                          // WHERE filter
    { field: "total_work", op: "gt", value: 1000, type: "having" },           // HAVING filter
    { expr: "[work_rank] <= 3", type: "qualify" }                             // QUALIFY filter
  ],
  groupBy: ["sublayer", "month"],
  orderBy: [{ column: "month", direction: "asc" }],
  limit: 50
}
```

## Benefits of This Structure

1. **Unified**: Everything is a column - no artificial separation
2. **Flexible**: Same column can be raw field, expression, or aggregation
3. **Composable**: Mix and match different column types freely
4. **Intuitive**: Clear what each column represents
5. **Extensible**: Easy to add new column behaviors
6. **Simple**: One concept to learn instead of multiple

## Migration Path

The current structure can be automatically converted:
- `dimensions` → `columns` with just `field` or `expr`
- `customColumns` → `columns` with `expr`
- `measures` → `columns` with `agg` or `window`
- `filters` remain the same

### 5. Simple Join Query
```typescript
{
  table: "progress_history",
  joins: [
    {
      table: "projects",
      type: "left",
      on: { left: "project_id", right: "id" }
    }
  ],
  columns: [
    { alias: "sublayer", field: "Sublayer", table: "progress_history" },
    { alias: "project_name", field: "name", table: "projects" },
    { alias: "total_work", field: "Work done", agg: "sum" }
  ],
  groupBy: ["sublayer", "project_name"],
  limit: 10
}
```

### 6. Complex Multi-Table Join with Expressions
```typescript
{
  table: { name: "progress_history", alias: "ph" },
  joins: [
    {
      table: { name: "projects", alias: "p" },
      type: "left",
      on: { left: "ph.project_id", right: "p.id" }
    },
    {
      table: { name: "contractors", alias: "c" },
      type: "inner",
      condition: "p.contractor_id = c.id AND c.active = true"
    }
  ],
  columns: [
    { alias: "sublayer", field: "Sublayer", table: "ph" },
    { alias: "project_name", field: "name", table: "p" },
    { alias: "contractor_name", field: "company_name", table: "c" },
    { alias: "efficiency", expr: "round([ph.Work done] / [ph.Scope] * 100, 2)" },
    { alias: "total_work", field: "Work done", table: "ph", agg: "sum" },
    { alias: "work_rank", field: "Work done", table: "ph", agg: "rank",
      partitionBy: ["p.name"], orderBy: "Work done" }
  ],
  filters: [
    { expr: "year([ph.Date]) = 2025", type: "where" },
    { field: "total_work", op: "gt", value: 1000, type: "having" },
    { expr: "[work_rank] <= 3", type: "qualify" }
  ],
  groupBy: ["sublayer", "project_name", "contractor_name"],
  orderBy: [{ column: "efficiency", direction: "desc" }],
  limit: 20
}
```

## Join Features

### Table References
- **Simple**: `table: "projects"`
- **With alias**: `table: { name: "projects", alias: "p" }`

### Join Conditions
- **Simple equality**: `on: { left: "project_id", right: "id" }`
- **Qualified fields**: `on: { left: "ph.project_id", right: "p.id" }`
- **Complex conditions**: `condition: "p.contractor_id = c.id AND c.active = true"`

### Field References
- **Unqualified**: `field: "Work done"` (when unambiguous)
- **Table qualified**: `field: "Work done", table: "ph"`
- **In expressions**: `expr: "[ph.Work done] / [ph.Scope]"`

### Join Types
- `inner` (default)
- `left`
- `right`
- `full`

This gives us the best of both worlds - simplicity and power!
