"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildQuery = buildQuery;
// queryBuilder.ts
var knex_1 = require("knex");
var pgsql_ast_parser_1 = require("pgsql-ast-parser");
var process = require("process");
// 1‑A  Knex init (pool, SSL, etc.)
var knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
// 1‑B  Allowed tables/columns (whitelist)
var ALLOW_LIST = {
    orders: ['id', 'region', 'sale_date', 'quantity', 'unit_price', 'amount'],
    customers: ['id', 'name', 'signup_channel'],
    progress_history: ['Activity', 'Subactivity', 'Layer', 'Sublayer', 'Date', 'Scope', 'Work done']
};
var SAFE_FUNCS = new Set([
    'sum', 'avg', 'min', 'max', 'count', 'count_distinct',
    'rank', 'dense_rank', 'row_number',
    'percentile_cont', 'percentile_disc',
    'lag', 'lead', 'sum', 'avg'
]);
function validateExpr(expr, allowedCols) {
    var ast = (0, pgsql_ast_parser_1.parse)("SELECT ".concat(expr))[0]; // Wrap to parse  :contentReference[oaicite:4]{index=4}
    var v = (0, pgsql_ast_parser_1.astVisitor)(function () { return ({
        ref: function (col) {
            if (!allowedCols.includes(col.name)) {
                throw new Error("Illegal column ".concat(col.name));
            }
        },
        funcCall: function (fn) {
            var name = fn.function.name.toLowerCase();
            if (!SAFE_FUNCS.has(name))
                throw new Error("Func ".concat(name, " not allowed"));
        },
        select: function () { },
        windowDef: function () { },
        aConst: function () { }, // ok
        insert: function () { throw 'Insert forbidden'; },
        update: function () { throw 'Update forbidden'; },
        delete: function () { throw 'Delete forbidden'; },
        selectStmt: function (inner) { if (inner !== ast)
            throw 'Sub‑selects forbidden'; }
    }); });
    v.statement(ast); // Walk AST – errors throw
}
// Helper function to generate date binning SQL expressions
function getDateBinExpression(field, dateBin) {
    var col = knex.ref(field).toSQL().sql;
    switch (dateBin) {
        case 'day':
            return "DATE(".concat(col, ")");
        case 'week':
            return "DATE_TRUNC('week', ".concat(col, ")");
        case 'month':
            return "DATE_TRUNC('month', ".concat(col, ")");
        case 'quarter':
            return "DATE_TRUNC('quarter', ".concat(col, ")");
        case 'year':
            return "DATE_TRUNC('year', ".concat(col, ")");
        case 'day_of_week':
            return "EXTRACT(DOW FROM ".concat(col, ")"); // 0=Sunday, 6=Saturday
        case 'day_of_month':
            return "EXTRACT(DAY FROM ".concat(col, ")");
        case 'day_of_year':
            return "EXTRACT(DOY FROM ".concat(col, ")");
        case 'week_of_year':
            return "EXTRACT(WEEK FROM ".concat(col, ")");
        case 'month_of_year':
            return "EXTRACT(MONTH FROM ".concat(col, ")");
        case 'quarter_of_year':
            return "EXTRACT(QUARTER FROM ".concat(col, ")");
        case 'year_of_era':
            return "EXTRACT(YEAR FROM ".concat(col, ")");
        case 'dont_bin':
        default:
            return col;
    }
}
// Helper function to normalize dimension to get field name and alias
function normalizeDimension(dim) {
    if (typeof dim === 'string') {
        return { field: dim, alias: dim, expression: knex.ref(dim).toSQL().sql };
    }
    var field = dim.field;
    var alias = dim.alias || (dim.dateBin && dim.dateBin !== 'dont_bin' ? "".concat(field, "_").concat(dim.dateBin) : field);
    var expression = dim.dateBin && dim.dateBin !== 'dont_bin'
        ? getDateBinExpression(field, dim.dateBin)
        : knex.ref(field).toSQL().sql;
    return { field: field, alias: alias, expression: expression };
}
function buildQuery(cfg) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    var allowed = ALLOW_LIST[cfg.table];
    if (!allowed)
        throw new Error('Table not allowed');
    var q = knex(cfg.table); // <table>
    /* 3‑A  Dimensions */
    var processedDimensions = [];
    ((_a = cfg.dimensions) !== null && _a !== void 0 ? _a : []).forEach(function (dim) {
        var normalized = normalizeDimension(dim);
        if (!allowed.includes(normalized.field))
            throw new Error("Dim ".concat(normalized.field, " not allowed"));
        if (normalized.expression === normalized.alias) {
            // Simple column reference
            q.select(normalized.alias);
        }
        else {
            // Date binning expression
            q.select(knex.raw("".concat(normalized.expression, " AS ??"), [normalized.alias]));
        }
        processedDimensions.push(normalized);
    });
    /* 3‑B  Custom (non‑agg) columns */
    ((_b = cfg.customColumns) !== null && _b !== void 0 ? _b : []).forEach(function (c) {
        validateExpr(c.expr, allowed);
        q.select(knex.raw("".concat(c.expr, " AS ??"), [c.alias]));
    });
    /* 3‑C  Measures */
    var groupables = __spreadArray(__spreadArray([], processedDimensions.map(function (d) { return d.alias; }), true), ((_c = cfg.customColumns) !== null && _c !== void 0 ? _c : []).map(function (c) { return c.alias; }), true);
    var hasGroupBy = !!(groupables.length && ((_d = cfg.measures) === null || _d === void 0 ? void 0 : _d.length));
    ((_e = cfg.measures) !== null && _e !== void 0 ? _e : []).forEach(function (m) { return addMeasure(q, m, allowed, hasGroupBy); });
    /* 3‑D  Filters — WHERE vs HAVING */
    ((_f = cfg.filters) !== null && _f !== void 0 ? _f : []).filter(function (f) { return !f.having; }).forEach(function (f) { return applyFilter(q, f, allowed, false); });
    ((_g = cfg.filters) !== null && _g !== void 0 ? _g : []).filter(function (f) { return f.having; }).forEach(function (f) { return applyFilter(q, f, allowed, true); });
    /* 3‑E  GROUP BY if any measures or HAVING */
    if (hasGroupBy)
        q.groupBy(groupables);
    /* 3‑F  ORDER / PAGINATION */
    ((_h = cfg.orderBy) !== null && _h !== void 0 ? _h : []).forEach(function (o) { return q.orderBy(o.field, o.direction); });
    if (cfg.limit)
        q.limit(Math.min(cfg.limit, 10000));
    if (cfg.offset)
        q.offset(cfg.offset);
    return q;
}
function addMeasure(q, m, allowed, hasGroupBy) {
    var _a, _b, _c, _d, _e, _f;
    var _g;
    if (hasGroupBy === void 0) { hasGroupBy = false; }
    var alias = (_g = m.alias) !== null && _g !== void 0 ? _g : (m.fn ? "".concat(m.fn, "_").concat(m.field || 'expr') : "".concat(m.agg, "_").concat(m.field));
    if (m.expr) { // raw expression
        validateExpr(m.expr, allowed);
        q.select(knex.raw("".concat(m.expr, " AS ??"), [alias]));
        return;
    }
    if (m.fn)
        return addWindowFn(q, m, alias, allowed, hasGroupBy); // window/analytic
    // simple aggregate
    if (!m.field || !allowed.includes(m.field))
        throw 'Bad measure field';
    switch (m.agg) {
        case 'sum':
            q.sum((_a = {}, _a[alias] = m.field, _a));
            break;
        case 'avg':
            q.avg((_b = {}, _b[alias] = m.field, _b));
            break;
        case 'min':
            q.min((_c = {}, _c[alias] = m.field, _c));
            break;
        case 'max':
            q.max((_d = {}, _d[alias] = m.field, _d));
            break;
        case 'count':
            q.count((_e = {}, _e[alias] = m.field, _e));
            break;
        case 'countDistinct':
            q.countDistinct((_f = {}, _f[alias] = m.field, _f));
            break;
        default: throw 'Unknown agg';
    }
}
function addWindowFn(q, m, alias, allowed, hasGroupBy) {
    var _a, _b, _c, _d, _e, _f;
    if (hasGroupBy === void 0) { hasGroupBy = false; }
    var col = knex.ref(m.field).toSQL().sql; // safe quote
    var order = knex.ref(m.orderBy || m.field).toSQL().sql;
    var partition = (_a = m.partitionBy) === null || _a === void 0 ? void 0 : _a.map(function (p) { return knex.ref(p).toSQL().sql; }).join(', ');
    // Helper function to wrap column reference with aggregation if needed
    var aggCol = function (fn) { return hasGroupBy ? "".concat(fn, "(").concat(col, ")") : col; };
    switch (m.fn) {
        case 'cumsum': // running total
            if (partition) {
                if (hasGroupBy) {
                    q.select(knex.raw("sum(sum(".concat(col, ")) OVER (PARTITION BY ").concat(partition, " ORDER BY ").concat(order, " ROWS UNBOUNDED PRECEDING) AS ??"), [alias]));
                    break;
                }
                else {
                    q.select(knex.raw("sum(".concat(col, ") OVER (PARTITION BY ").concat(partition, " ORDER BY ").concat(order, " ROWS UNBOUNDED PRECEDING) AS ??"), [alias]));
                    break;
                }
            }
            else {
                if (hasGroupBy) {
                    q.select(knex.raw("sum(sum(".concat(col, ")) OVER (ORDER BY ").concat(order, " ROWS UNBOUNDED PRECEDING) AS ??"), [alias]));
                    break;
                }
                else {
                    q.select(knex.raw("sum(".concat(col, ") OVER (ORDER BY ").concat(order, " ROWS UNBOUNDED PRECEDING) AS ??"), [alias]));
                    break;
                }
            }
        case 'cumcount':
            q.select(knex.raw("count(*) OVER (ORDER BY ".concat(order, ") AS ??"), [alias]));
            break;
        case 'moving_avg': {
            var rows = (_c = (_b = m.frame) === null || _b === void 0 ? void 0 : _b.preceding) !== null && _c !== void 0 ? _c : 2;
            q.select(knex.raw("avg(".concat(aggCol('sum'), ") OVER (ORDER BY ").concat(order, " ROWS BETWEEN ? PRECEDING AND CURRENT ROW) AS ??"), [rows, alias]));
            break;
        }
        case 'moving_sum': {
            var rows = (_e = (_d = m.frame) === null || _d === void 0 ? void 0 : _d.preceding) !== null && _e !== void 0 ? _e : 2;
            q.select(knex.raw("sum(".concat(aggCol('sum'), ") OVER (ORDER BY ").concat(order, " ROWS BETWEEN ? PRECEDING AND CURRENT ROW) AS ??"), [rows, alias]));
            break;
        }
        case 'median':
            q.select(knex.raw("percentile_cont(0.5) WITHIN GROUP (ORDER BY ".concat(aggCol('sum'), ") AS ??"), [alias]));
            break;
        case 'percentile':
            q.select(knex.raw("percentile_cont(?) WITHIN GROUP (ORDER BY ".concat(aggCol('sum'), ") AS ??"), [(_f = m.percentile) !== null && _f !== void 0 ? _f : 0.9, alias]));
            break;
        case 'delta':
            var deltaExpr = hasGroupBy ? "sum(".concat(col, ") - lag(sum(").concat(col, "))") : "".concat(col, " - lag(").concat(col, ")");
            q.select(knex.raw("".concat(deltaExpr, " OVER (ORDER BY ").concat(order, ") AS ??"), [alias]));
            break;
        case 'pct_change':
            var pctExpr = hasGroupBy
                ? "100.0 * (sum(".concat(col, ") - lag(sum(").concat(col, ")) OVER (ORDER BY ").concat(order, ")) / NULLIF(lag(sum(").concat(col, ")) OVER (ORDER BY ").concat(order, "),0)")
                : "100.0 * (".concat(col, " - lag(").concat(col, ") OVER (ORDER BY ").concat(order, ")) / NULLIF(lag(").concat(col, ") OVER (ORDER BY ").concat(order, "),0)");
            q.select(knex.raw("".concat(pctExpr, " AS ??"), [alias]));
            break;
        case 'rank':
            var rankCol = hasGroupBy ? "sum(".concat(col, ")") : col;
            q.select(knex.raw("rank() OVER (ORDER BY ".concat(rankCol, " ").concat(order.includes('DESC') ? 'DESC' : 'ASC', ") AS ??"), [alias]));
            break;
        case 'dense_rank':
            var denseRankCol = hasGroupBy ? "sum(".concat(col, ")") : col;
            q.select(knex.raw("dense_rank() OVER (ORDER BY ".concat(denseRankCol, " ").concat(order.includes('DESC') ? 'DESC' : 'ASC', ") AS ??"), [alias]));
            break;
        case 'row_number':
            q.select(knex.raw("row_number() OVER (ORDER BY ".concat(order, ") AS ??"), [alias]));
            break;
        default: throw 'Window fn not supported';
    }
}
function applyFilter(q, f, allowed, having) {
    if (having === void 0) { having = false; }
    // alias OK in HAVING; otherwise enforce physical column
    if (!having && !allowed.includes(f.field))
        throw new Error("Bad filter field ".concat(f.field));
    var builder = having ? q.having.bind(q) : q.where.bind(q);
    switch (f.op) {
        case 'eq':
            builder(f.field, '=', f.value);
            break;
        case 'neq':
            builder(f.field, '!=', f.value);
            break;
        case 'gt':
            builder(f.field, '>', f.value);
            break;
        case 'lt':
            builder(f.field, '<', f.value);
            break;
        case 'gte':
            builder(f.field, '>=', f.value);
            break;
        case 'lte':
            builder(f.field, '<=', f.value);
            break;
        case 'between':
            builder(f.field, 'between', f.value);
            break;
        case 'in':
            builder(f.field, 'in', f.value);
            break;
        case 'like':
            builder(f.field, 'like', f.value);
            break;
        default: throw 'Unsupported op';
    }
}
