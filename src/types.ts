// Core type definitions for the unified query builder

export interface QueryConfig {
  // Tables and joins
  table: string | TableRef;         // Main table (can be string or object with alias)
  joins?: Join[];                   // Additional tables to join
  
  // All columns (raw fields, expressions, aggregations)
  columns: Column[];
  
  // Filters (traditional or expression-based)
  filters?: Filter[];
  
  // Grouping, ordering, pagination
  groupBy?: string[];               // Column aliases to group by (auto-determined if not provided)
  orderBy?: OrderBy[];
  limit?: number;
  offset?: number;
}

export interface TableRef {
  name: string;                     // Table name
  alias?: string;                   // Table alias
}

export interface Join {
  table: string | TableRef;         // Table to join
  type?: JoinType;                  // Join type (default: 'inner')
  
  // Join condition - Option 1: Simple field equality
  on?: {
    left: string;                   // Field from left table (table.field or just field)
    right: string;                  // Field from right table (table.field or just field)
  };
  
  // Join condition - Option 2: Complex expression
  condition?: string;               // Full join condition expression
}

export interface Column {
  alias?: string;                   // Column alias (auto-generated if not provided)
  
  // Option 1: Raw field
  field?: string;                   // "Work done", "Date", "table.field", etc.
  table?: string;                   // Table name/alias (optional if unambiguous)
  
  // Option 2: Expression (Metabase-style)
  expr?: string;                    // "abs([Work done] - [Scope])", "year([Date])", etc.
  
  // Option 3: Aggregation (includes regular agg + window functions)
  agg?: AggregationType;
  
  // Window function controls (only used with window agg functions)
  partitionBy?: string[];           // PARTITION BY columns
  orderBy?: string | string[];      // ORDER BY columns  
  frame?: WindowFrame;              // Window frame (for moving functions)
  
  // Special behaviors
  dateBin?: DateBin;                // For date fields
  binCount?: number;                // For numeric histograms
}

export interface Filter {
  // Option 1: Traditional field filter
  field?: string;
  table?: string;                   // Table name/alias (optional if unambiguous)
  op?: FilterOperator;
  value?: any;
  
  // Option 2: Expression filter
  expr?: string;                    // "abs([Work done] - [Scope]) > 1000"
  
  // Filter placement (default: 'where')
  type?: FilterType;
}

export interface OrderBy {
  column: string;                   // Column alias
  direction?: 'asc' | 'desc';       // Default: 'asc'
}

export interface WindowFrame {
  preceding?: number;               // ROWS n PRECEDING
  following?: number;               // ROWS n FOLLOWING
}

// Enums and type unions
export type JoinType = 'inner' | 'left' | 'right' | 'full';

export type AggregationType = 
  // Regular aggregations
  | 'sum' | 'avg' | 'count' | 'max' | 'min' | 'median' | 'stddev' | 'variance'
  // Cumulative window functions
  | 'cumsum' | 'cumavg' | 'cumcount' | 'cummax' | 'cummin'
  // Moving window functions  
  | 'moving_avg' | 'moving_sum' | 'moving_count' | 'moving_max' | 'moving_min'
  // Analytical functions
  | 'delta' | 'pct_change' | 'lag' | 'lead'
  // Ranking functions
  | 'rank' | 'dense_rank' | 'row_number' | 'percent_rank' | 'ntile';

export type FilterOperator = 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'between' | 'in' | 'like' | 'ilike';

export type FilterType = 'where' | 'having' | 'qualify';

export type DateBin = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 
                     'day_of_week' | 'month_of_year' | 'quarter_of_year';

// Helper types for internal processing
export interface ProcessedColumn extends Column {
  alias: string;                    // Always present after processing
  isAggregated: boolean;           // Whether this column uses aggregation
  isWindowFunction: boolean;       // Whether this column uses window functions
  sqlExpression: string;           // The final SQL expression
}

export interface ProcessedTable {
  name: string;
  alias?: string;
  qualifiedName: string;           // name or alias for SQL generation
}

export interface ProcessedJoin extends Join {
  table: ProcessedTable;
  sqlCondition: string;            // The final SQL join condition
}

// Configuration for expression parsing
export interface ExpressionContext {
  allowedColumns: string[];
  tables: ProcessedTable[];
  hasGroupBy: boolean;
  processedColumns: ProcessedColumn[];
}
