// Table and join utilities

import { TableRef, Join, ProcessedTable, ProcessedJoin } from '../types';
import { parseExpression } from '../expressions/parser';

/**
 * Process a table reference into a standardized format
 */
export function processTable(table: string | TableRef): ProcessedTable {
  if (typeof table === 'string') {
    return {
      name: table,
      qualifiedName: table
    };
  }
  
  return {
    name: table.name,
    alias: table.alias,
    qualifiedName: table.alias || table.name
  };
}

/**
 * Process joins and generate SQL conditions
 */
export function processJoins(joins: Join[], mainTable: ProcessedTable, allowedColumns: string[]): ProcessedJoin[] {
  return joins.map(join => {
    const processedTable = processTable(join.table);
    
    let sqlCondition: string;
    
    if (join.condition) {
      // Use custom condition as-is (could be enhanced to parse expressions)
      sqlCondition = join.condition;
    } else if (join.on) {
      // Generate condition from simple field equality
      const leftField = qualifyFieldName(join.on.left, mainTable);
      const rightField = qualifyFieldName(join.on.right, processedTable);
      sqlCondition = `${leftField} = ${rightField}`;
    } else {
      throw new Error(`Join must have either 'on' or 'condition' specified`);
    }
    
    return {
      ...join,
      table: processedTable,
      type: join.type || 'inner',
      sqlCondition
    };
  });
}

/**
 * Qualify a field name with table if needed
 */
export function qualifyFieldName(field: string, table: ProcessedTable): string {
  // If field already contains a dot, assume it's already qualified
  if (field.includes('.')) {
    return field;
  }
  
  // Otherwise, qualify with table name/alias
  return `${table.qualifiedName}.${field}`;
}

/**
 * Extract table name from a qualified field (e.g., "table.field" -> "table")
 */
export function extractTableFromField(field: string): string | null {
  const parts = field.split('.');
  return parts.length > 1 ? parts[0] : null;
}

/**
 * Extract field name from a qualified field (e.g., "table.field" -> "field")
 */
export function extractFieldFromQualified(field: string): string {
  const parts = field.split('.');
  return parts[parts.length - 1];
}

/**
 * Get all table names/aliases from the query
 */
export function getAllTableNames(mainTable: ProcessedTable, joins: ProcessedJoin[]): string[] {
  const names = [mainTable.qualifiedName];
  joins.forEach(join => {
    names.push(join.table.qualifiedName);
  });
  return names;
}

/**
 * Find which table a field belongs to (if specified)
 */
export function findTableForField(field: string, tables: ProcessedTable[]): ProcessedTable | null {
  const tableName = extractTableFromField(field);
  if (!tableName) {
    return null;
  }
  
  return tables.find(table => 
    table.qualifiedName === tableName || table.name === tableName
  ) || null;
}

/**
 * Validate that all referenced tables exist
 */
export function validateTableReferences(
  fields: string[], 
  mainTable: ProcessedTable, 
  joins: ProcessedJoin[]
): void {
  const allTables = [mainTable, ...joins.map(j => j.table)];
  const tableNames = getAllTableNames(mainTable, joins);
  
  for (const field of fields) {
    const tableName = extractTableFromField(field);
    if (tableName && !tableNames.includes(tableName)) {
      throw new Error(`Referenced table '${tableName}' not found in query. Available tables: ${tableNames.join(', ')}`);
    }
  }
}

/**
 * Generate SQL for table and joins
 */
export function generateTableSQL(mainTable: ProcessedTable, joins: ProcessedJoin[]): string {
  let sql = mainTable.alias 
    ? `"${mainTable.name}" AS "${mainTable.alias}"`
    : `"${mainTable.name}"`;
  
  for (const join of joins) {
    const joinType = join.type!.toUpperCase();
    const tableSql = join.table.alias
      ? `"${join.table.name}" AS "${join.table.alias}"`
      : `"${join.table.name}"`;
    
    sql += ` ${joinType} JOIN ${tableSql} ON ${join.sqlCondition}`;
  }
  
  return sql;
}

/**
 * Get all columns that should be available for reference
 */
export function getAllowedColumns(mainTable: ProcessedTable, joins: ProcessedJoin[]): string[] {
  // This is a simplified version - in a real implementation, you'd want to
  // introspect the database schema to get actual column names
  const tables = [mainTable, ...joins.map(j => j.table)];
  
  // For now, return table names so expressions can reference them
  // In practice, you'd query the database schema
  return tables.map(t => t.qualifiedName);
}
