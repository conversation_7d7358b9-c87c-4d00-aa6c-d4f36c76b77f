// Filter processing and SQL generation

import knexFactory from 'knex';
import * as process from 'process';
import { Filter, ProcessedTable, ProcessedJoin, ExpressionContext } from '../types';
import { applyFilterDefaults, validateFilter } from '../utils/defaults';
import { qualifyFieldName, findTableForField } from '../utils/tableUtils';
import { parseExpression } from '../expressions/parser';

const knex = knexFactory({ client: 'pg', connection: process.env.PG_URL });

/**
 * Process and apply filters to a query
 */
export function applyFilters(
  query: any,
  filters: Filter[],
  mainTable: ProcessedTable,
  joins: ProcessedJoin[],
  allowedColumns: string[],
  hasGroupBy: boolean
): void {
  if (!filters || filters.length === 0) return;

  const allTables = [mainTable, ...joins.map(j => j.table)];
  
  // Group filters by type
  const whereFilters = filters.filter(f => (f.type || 'where') === 'where');
  const havingFilters = filters.filter(f => f.type === 'having');
  const qualifyFilters = filters.filter(f => f.type === 'qualify');

  // Apply WHERE filters
  whereFilters.forEach(filter => {
    const processedFilter = applyFilterDefaults(filter);
    validateFilter(processedFilter);
    applyFilter(query, processedFilter, allTables, allowedColumns, false, hasGroupBy);
  });

  // Apply HAVING filters
  havingFilters.forEach(filter => {
    const processedFilter = applyFilterDefaults(filter);
    validateFilter(processedFilter);
    applyFilter(query, processedFilter, allTables, allowedColumns, true, hasGroupBy);
  });

  // Apply QUALIFY filters (PostgreSQL doesn't support QUALIFY, so we'll use a subquery approach)
  if (qualifyFilters.length > 0) {
    console.warn('QUALIFY filters are not yet fully supported in PostgreSQL. Consider using HAVING instead.');
    // TODO: Implement QUALIFY support using subqueries
  }
}

/**
 * Apply a single filter to the query
 */
function applyFilter(
  query: any,
  filter: Filter,
  tables: ProcessedTable[],
  allowedColumns: string[],
  isHaving: boolean,
  hasGroupBy: boolean
): void {
  const builder = isHaving ? query.having.bind(query) : query.where.bind(query);
  
  if (filter.expr) {
    // Handle expression-based filters
    const context: ExpressionContext = {
      allowedColumns,
      tables,
      hasGroupBy: isHaving,
      processedColumns: [] // This would be populated in a real implementation
    };
    
    // Parse Metabase-style expressions or use raw SQL
    const sqlExpression = filter.expr.includes('[') || (filter.expr.includes('(') && !filter.expr.startsWith('('))
      ? parseExpression(filter.expr, context)
      : filter.expr;
      
    builder(knex.raw(sqlExpression));
  } else if (filter.field) {
    // Handle traditional field-based filters
    const qualifiedField = processFieldForFilter(filter.field, filter.table, tables);
    
    if (!filter.op) {
      throw new Error('Filter with field must have op');
    }
    
    switch (filter.op) {
      case 'eq':
        builder(qualifiedField, '=', filter.value);
        break;
      case 'neq':
        builder(qualifiedField, '!=', filter.value);
        break;
      case 'gt':
        builder(qualifiedField, '>', filter.value);
        break;
      case 'lt':
        builder(qualifiedField, '<', filter.value);
        break;
      case 'gte':
        builder(qualifiedField, '>=', filter.value);
        break;
      case 'lte':
        builder(qualifiedField, '<=', filter.value);
        break;
      case 'between':
        if (!Array.isArray(filter.value) || filter.value.length !== 2) {
          throw new Error('Between filter requires array of 2 values');
        }
        builder(qualifiedField, 'between', filter.value);
        break;
      case 'in':
        if (!Array.isArray(filter.value)) {
          throw new Error('In filter requires array of values');
        }
        builder(qualifiedField, 'in', filter.value);
        break;
      case 'like':
        builder(qualifiedField, 'like', filter.value);
        break;
      case 'ilike':
        builder(qualifiedField, 'ilike', filter.value);
        break;
      default:
        throw new Error(`Unsupported filter operator: ${filter.op}`);
    }
  } else {
    throw new Error('Filter must have either field or expr');
  }
}

/**
 * Process a field reference for filtering
 */
function processFieldForFilter(
  field: string,
  explicitTable: string | undefined,
  tables: ProcessedTable[]
): string {
  if (explicitTable) {
    // Use explicitly specified table
    const table = tables.find(t => t.qualifiedName === explicitTable || t.name === explicitTable);
    if (!table) {
      throw new Error(`Table '${explicitTable}' not found`);
    }
    return qualifyFieldName(field, table);
  } else if (field.includes('.')) {
    // Field is already qualified
    const table = findTableForField(field, tables);
    if (!table) {
      const tableName = field.split('.')[0];
      throw new Error(`Table '${tableName}' not found`);
    }
    return field;
  } else {
    // Unqualified field - return as-is (could be enhanced to auto-detect table)
    return field;
  }
}
