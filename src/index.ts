// Main entry point for the query builder package
export * from './queryBuilder';

// Re-export commonly used types for convenience
export type {
  ChartConfig,
  QueryConfig,
  Column,
  Filter,
  TableRef,
  Join,
  OrderBy,
  AggregationType,
  FilterOperator,
  FilterType,
  DateBin,
  JoinType
} from './queryBuilder';

// Helper function for examples to run queries and exit cleanly
export async function runExample(name: string, queryFn: () => Promise<void>) {
  console.log(`=== ${name} ===\n`);
  try {
    await queryFn();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    const { cleanup } = await import('./queryBuilder');
    await cleanup();
    process.exit(0);
  }
}
