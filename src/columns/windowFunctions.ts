// Window function SQL generation

import { AggregationType, WindowFrame, ExpressionContext } from '../types';

/**
 * Generate SQL for window functions
 */
export function generateWindowFunctionSQL(
  agg: AggregationType,
  expression: string,
  partitionBy?: string[],
  orderBy?: string | string[],
  frame?: WindowFrame,
  context?: ExpressionContext
): string {
  // Determine if we need nested aggregation (when there's a GROUP BY clause)
  const hasGroupBy = context?.hasGroupBy || false;
  const windowClause = generateWindowClause(partitionBy, orderBy, frame, agg, hasGroupBy);

  // For window functions that operate on aggregated data, we need nested aggregation
  const needsNestedAgg = hasGroupBy && needsAggregationNesting(agg);
  const finalExpression = needsNestedAgg ? wrapWithAggregation(expression, agg) : expression;

  switch (agg) {
    // Cumulative functions
    case 'cumsum':
      return `SUM(${finalExpression}) OVER (${windowClause})`;

    case 'cumavg':
      return `AVG(${finalExpression}) OVER (${windowClause})`;

    case 'cumcount':
      return `COUNT(${finalExpression}) OVER (${windowClause})`;

    case 'cummax':
      return `MAX(${finalExpression}) OVER (${windowClause})`;

    case 'cummin':
      return `MIN(${finalExpression}) OVER (${windowClause})`;

    // Moving functions
    case 'moving_avg':
      return `AVG(${finalExpression}) OVER (${windowClause})`;

    case 'moving_sum':
      return `SUM(${finalExpression}) OVER (${windowClause})`;

    case 'moving_count':
      return `COUNT(${finalExpression}) OVER (${windowClause})`;

    case 'moving_max':
      return `MAX(${finalExpression}) OVER (${windowClause})`;

    case 'moving_min':
      return `MIN(${finalExpression}) OVER (${windowClause})`;

    // Analytical functions
    case 'delta':
      const deltaOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
      return `${finalExpression} - LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, deltaOrderBy, undefined, agg, hasGroupBy)})`;

    case 'pct_change':
      const pctOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
      return `100.0 * (${finalExpression} - LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, pctOrderBy, undefined, agg, hasGroupBy)})) / NULLIF(LAG(${finalExpression}) OVER (${generateWindowClause(partitionBy, pctOrderBy, undefined, agg, hasGroupBy)}),0)`;

    case 'lag':
      return `LAG(${finalExpression}) OVER (${windowClause})`;

    case 'lead':
      return `LEAD(${finalExpression}) OVER (${windowClause})`;

    // Ranking functions (these don't need the expression, just the window clause)
    case 'rank':
      return `RANK() OVER (${windowClause})`;

    case 'dense_rank':
      return `DENSE_RANK() OVER (${windowClause})`;

    case 'row_number':
      return `ROW_NUMBER() OVER (${windowClause})`;

    case 'percent_rank':
      return `PERCENT_RANK() OVER (${windowClause})`;

    case 'ntile':
      // For ntile, we'd need an additional parameter for the number of buckets
      // For now, default to 4 quartiles
      return `NTILE(4) OVER (${windowClause})`;

    default:
      throw new Error(`Unsupported window function: ${agg}`);
  }
}

/**
 * Check if window function needs nested aggregation when there's GROUP BY
 */
function needsAggregationNesting(agg: AggregationType): boolean {
  // Functions that operate on values need nested aggregation with GROUP BY
  return [
    'cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin',
    'moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min',
    'delta', 'pct_change', 'lag', 'lead'
  ].includes(agg);
}

/**
 * Wrap expression with appropriate aggregation for nested window functions
 */
function wrapWithAggregation(expression: string, agg: AggregationType): string {
  // For most window functions, we use SUM for the inner aggregation
  // This handles cases like SUM(SUM("field")) OVER (...)
  switch (agg) {
    case 'cumsum':
    case 'moving_sum':
      return `SUM(${expression})`;

    case 'cumavg':
    case 'moving_avg':
      return `AVG(${expression})`;

    case 'cumcount':
    case 'moving_count':
      return `COUNT(${expression})`;

    case 'cummax':
    case 'moving_max':
      return `MAX(${expression})`;

    case 'cummin':
    case 'moving_min':
      return `MIN(${expression})`;

    case 'delta':
    case 'pct_change':
    case 'lag':
    case 'lead':
      // For analytical functions, use SUM as default
      return `SUM(${expression})`;

    default:
      return `SUM(${expression})`;
  }
}

/**
 * Generate the window clause (PARTITION BY, ORDER BY, frame)
 */
function generateWindowClause(
  partitionBy?: string[],
  orderBy?: string | string[],
  frame?: WindowFrame,
  agg?: AggregationType,
  hasGroupBy?: boolean
): string {
  const parts: string[] = [];

  // PARTITION BY - use actual column names, not aliases
  if (partitionBy && partitionBy.length > 0) {
    const partitionCols = partitionBy.map(col => resolveColumnReference(col, hasGroupBy));
    parts.push(`PARTITION BY ${partitionCols.join(', ')}`);
  }

  // ORDER BY - use actual column names, not aliases
  if (orderBy) {
    const orderCols = Array.isArray(orderBy) ? orderBy : [orderBy];
    const resolvedOrderCols = orderCols.map(col => resolveColumnReference(col, hasGroupBy));
    parts.push(`ORDER BY ${resolvedOrderCols.join(', ')}`);
  }

  // Window frame
  if (frame || needsDefaultFrame(agg)) {
    const frameClause = generateFrameClause(frame, agg);
    if (frameClause) {
      parts.push(frameClause);
    }
  }

  return parts.join(' ');
}

/**
 * Generate the frame clause for window functions
 */
function generateFrameClause(frame?: WindowFrame, agg?: AggregationType): string {
  // Default frames for different function types
  if (!frame) {
    if (agg && isCumulativeFunction(agg)) {
      return 'ROWS UNBOUNDED PRECEDING';
    }
    if (agg && isMovingFunction(agg)) {
      return 'ROWS BETWEEN 6 PRECEDING AND CURRENT ROW'; // Default 7-day window
    }
    return '';
  }

  const parts: string[] = ['ROWS BETWEEN'];

  if (frame.preceding !== undefined) {
    if (frame.preceding === Infinity) {
      parts.push('UNBOUNDED PRECEDING');
    } else {
      parts.push(`${frame.preceding} PRECEDING`);
    }
  } else {
    parts.push('CURRENT ROW');
  }

  parts.push('AND');

  if (frame.following !== undefined) {
    if (frame.following === Infinity) {
      parts.push('UNBOUNDED FOLLOWING');
    } else {
      parts.push(`${frame.following} FOLLOWING`);
    }
  } else {
    parts.push('CURRENT ROW');
  }

  return parts.join(' ');
}

/**
 * Check if aggregation needs a default frame
 */
function needsDefaultFrame(agg?: AggregationType): boolean {
  return !!(agg && (isCumulativeFunction(agg) || isMovingFunction(agg)));
}

/**
 * Check if aggregation is a cumulative function
 */
function isCumulativeFunction(agg: AggregationType): boolean {
  return ['cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin'].includes(agg);
}

/**
 * Check if aggregation is a moving function
 */
function isMovingFunction(agg: AggregationType): boolean {
  return ['moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min'].includes(agg);
}

/**
 * Resolve column reference for window functions
 * Converts aliases back to actual column references
 */
function resolveColumnReference(col: string, hasGroupBy?: boolean): string {
  // Handle common alias patterns and convert back to actual column references
  if (col === 'month' || col === 'date_month') {
    return hasGroupBy ? `DATE_TRUNC('month', "Date")` : `"Date"`;
  }
  if (col === 'week' || col === 'date_week') {
    return hasGroupBy ? `DATE_TRUNC('week', "Date")` : `"Date"`;
  }
  if (col === 'quarter' || col === 'date_quarter') {
    return hasGroupBy ? `DATE_TRUNC('quarter', "Date")` : `"Date"`;
  }
  if (col === 'year' || col === 'date_year') {
    return hasGroupBy ? `DATE_TRUNC('year', "Date")` : `"Date"`;
  }

  // Special handling for columns when there's GROUP BY
  if (hasGroupBy) {
    // When there's GROUP BY, we can't reference raw columns in window functions
    // unless they're in the GROUP BY clause. For ORDER BY in window functions,
    // we need to use aggregated versions or grouped columns.

    // For Date column, use MIN(Date) to get a representative date for each group
    if (col === 'Date') {
      return `MIN("Date")`;
    }

    // For other columns that might not be in GROUP BY, use MIN as default
    // This is a reasonable approach for window function ordering
    if (col !== 'Sublayer') { // Assuming Sublayer is commonly in GROUP BY
      return `MIN("${col}")`;
    }
  }

  // For regular column names, just quote them
  return `"${col}"`;
}
