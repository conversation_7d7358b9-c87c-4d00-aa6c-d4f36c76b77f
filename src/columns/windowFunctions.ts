// Window function SQL generation

import { AggregationType, WindowFrame, ExpressionContext } from '../types';

/**
 * Generate SQL for window functions
 */
export function generateWindowFunctionSQL(
  agg: AggregationType,
  expression: string,
  partitionBy?: string[],
  orderBy?: string | string[],
  frame?: WindowFrame,
  context?: ExpressionContext
): string {
  const windowClause = generateWindowClause(partitionBy, orderBy, frame, agg);
  
  switch (agg) {
    // Cumulative functions
    case 'cumsum':
      return `SUM(${expression}) OVER (${windowClause})`;
    
    case 'cumavg':
      return `AVG(${expression}) OVER (${windowClause})`;
    
    case 'cumcount':
      return `COUNT(${expression}) OVER (${windowClause})`;
    
    case 'cummax':
      return `MAX(${expression}) OVER (${windowClause})`;
    
    case 'cummin':
      return `MIN(${expression}) OVER (${windowClause})`;
    
    // Moving functions
    case 'moving_avg':
      return `AVG(${expression}) OVER (${windowClause})`;
    
    case 'moving_sum':
      return `SUM(${expression}) OVER (${windowClause})`;
    
    case 'moving_count':
      return `COUNT(${expression}) OVER (${windowClause})`;
    
    case 'moving_max':
      return `MAX(${expression}) OVER (${windowClause})`;
    
    case 'moving_min':
      return `MIN(${expression}) OVER (${windowClause})`;
    
    // Analytical functions
    case 'delta':
      const deltaOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
      return `${expression} - LAG(${expression}) OVER (${generateWindowClause(partitionBy, deltaOrderBy)})`;
    
    case 'pct_change':
      const pctOrderBy = orderBy ? (Array.isArray(orderBy) ? orderBy[0] : orderBy) : '';
      return `100.0 * (${expression} - LAG(${expression}) OVER (${generateWindowClause(partitionBy, pctOrderBy)})) / NULLIF(LAG(${expression}) OVER (${generateWindowClause(partitionBy, pctOrderBy)}),0)`;
    
    case 'lag':
      return `LAG(${expression}) OVER (${windowClause})`;
    
    case 'lead':
      return `LEAD(${expression}) OVER (${windowClause})`;
    
    // Ranking functions
    case 'rank':
      return `RANK() OVER (${windowClause})`;
    
    case 'dense_rank':
      return `DENSE_RANK() OVER (${windowClause})`;
    
    case 'row_number':
      return `ROW_NUMBER() OVER (${windowClause})`;
    
    case 'percent_rank':
      return `PERCENT_RANK() OVER (${windowClause})`;
    
    case 'ntile':
      // For ntile, we'd need an additional parameter for the number of buckets
      // For now, default to 4 quartiles
      return `NTILE(4) OVER (${windowClause})`;
    
    default:
      throw new Error(`Unsupported window function: ${agg}`);
  }
}

/**
 * Generate the window clause (PARTITION BY, ORDER BY, frame)
 */
function generateWindowClause(
  partitionBy?: string[],
  orderBy?: string | string[],
  frame?: WindowFrame,
  agg?: AggregationType
): string {
  const parts: string[] = [];
  
  // PARTITION BY
  if (partitionBy && partitionBy.length > 0) {
    parts.push(`PARTITION BY ${partitionBy.map(col => `"${col}"`).join(', ')}`);
  }
  
  // ORDER BY
  if (orderBy) {
    const orderCols = Array.isArray(orderBy) ? orderBy : [orderBy];
    parts.push(`ORDER BY ${orderCols.map(col => `"${col}"`).join(', ')}`);
  }
  
  // Window frame
  if (frame || needsDefaultFrame(agg)) {
    const frameClause = generateFrameClause(frame, agg);
    if (frameClause) {
      parts.push(frameClause);
    }
  }
  
  return parts.join(' ');
}

/**
 * Generate the frame clause for window functions
 */
function generateFrameClause(frame?: WindowFrame, agg?: AggregationType): string {
  // Default frames for different function types
  if (!frame) {
    if (agg && isCumulativeFunction(agg)) {
      return 'ROWS UNBOUNDED PRECEDING';
    }
    if (agg && isMovingFunction(agg)) {
      return 'ROWS BETWEEN 6 PRECEDING AND CURRENT ROW'; // Default 7-day window
    }
    return '';
  }
  
  const parts: string[] = ['ROWS BETWEEN'];
  
  if (frame.preceding !== undefined) {
    if (frame.preceding === Infinity) {
      parts.push('UNBOUNDED PRECEDING');
    } else {
      parts.push(`${frame.preceding} PRECEDING`);
    }
  } else {
    parts.push('CURRENT ROW');
  }
  
  parts.push('AND');
  
  if (frame.following !== undefined) {
    if (frame.following === Infinity) {
      parts.push('UNBOUNDED FOLLOWING');
    } else {
      parts.push(`${frame.following} FOLLOWING`);
    }
  } else {
    parts.push('CURRENT ROW');
  }
  
  return parts.join(' ');
}

/**
 * Check if aggregation needs a default frame
 */
function needsDefaultFrame(agg?: AggregationType): boolean {
  return !!(agg && (isCumulativeFunction(agg) || isMovingFunction(agg)));
}

/**
 * Check if aggregation is a cumulative function
 */
function isCumulativeFunction(agg: AggregationType): boolean {
  return ['cumsum', 'cumavg', 'cumcount', 'cummax', 'cummin'].includes(agg);
}

/**
 * Check if aggregation is a moving function
 */
function isMovingFunction(agg: AggregationType): boolean {
  return ['moving_avg', 'moving_sum', 'moving_count', 'moving_max', 'moving_min'].includes(agg);
}
