// Numeric binning utilities

import knexFactory from 'knex';
import * as process from 'process';

const knex = knexFactory({ client: 'pg', connection: process.env.PG_URL });

/**
 * Generate SQL expression for numeric binning using WIDTH_BUCKET
 */
export function generateNumericBinExpression(field: string, binCount: number, originalField: string): string {
  // field is already quoted by this point
  // originalField is the unquoted field name for the subquery
  
  const quotedOriginalField = knex.ref(originalField).toSQL().sql;
  
  return `WIDTH_BUCKET(${field},
        (SELECT MIN(${quotedOriginalField}) FROM (SELECT ${quotedOriginalField} FROM "progress_history") t),
        (SELECT MAX(${quotedOriginalField}) FROM (SELECT ${quotedOriginalField} FROM "progress_history") t),
        ${binCount})`;
}
