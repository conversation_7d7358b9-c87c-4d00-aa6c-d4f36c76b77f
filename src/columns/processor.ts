// Column processing and SQL generation

import knexFactory from 'knex';
import * as process from 'process';
import { Column, ProcessedColumn, ProcessedTable, ProcessedJoin, ExpressionContext } from '../types';
import {
  generateColumnAlias,
  validateColumn,
  isColumnAggregated,
  isWindowFunction,
  isRegularAggregation
} from '../utils/defaults';
import { qualifyFieldName, extractTableFromField, findTableForField } from '../utils/tableUtils';
import { parseExpression } from '../expressions/parser';
import { generateDateBinExpression } from './dateBinning';
import { generateNumericBinExpression } from './numericBinning';
import { generateAggregationSQL } from './aggregations';
import { generateWindowFunctionSQL } from './windowFunctions';

const knex = knexFactory({ client: 'pg', connection: process.env.PG_URL });

/**
 * Process all columns and generate their SQL expressions
 */
export function processColumns(
  columns: Column[],
  mainTable: ProcessedTable,
  joins: ProcessedJoin[],
  allowedColumns: string[],
  hasGroupBy?: boolean
): ProcessedColumn[] {
  const allTables = [mainTable, ...joins.map(j => j.table)];
  const processedColumns: ProcessedColumn[] = [];

  // First pass: process all columns to build the context
  columns.forEach(column => {
    validateColumn(column);

    const alias = generateColumnAlias(column);
    const isAggregated = isColumnAggregated(column);
    const isWindowFn = !!(column.agg && isWindowFunction(column.agg));

    // Create basic processed column (without SQL expression yet)
    const processedColumn: ProcessedColumn = {
      ...column,
      alias,
      isAggregated,
      isWindowFunction: isWindowFn,
      sqlExpression: '' // Will be filled in second pass
    };

    processedColumns.push(processedColumn);
  });

  // Second pass: generate SQL expressions with full context
  processedColumns.forEach((processedColumn, index) => {
    const column = columns[index];

    // Create expression context with all processed columns
    const context: ExpressionContext = {
      allowedColumns,
      tables: allTables,
      hasGroupBy: hasGroupBy || false,
      processedColumns: processedColumns
    };

    processedColumn.sqlExpression = generateColumnSQL(column, context, allTables);
  });

  return processedColumns;
}

/**
 * Generate SQL expression for a single column
 */
function generateColumnSQL(
  column: Column,
  context: ExpressionContext,
  tables: ProcessedTable[]
): string {
  let baseExpression: string;

  if (column.expr) {
    // Handle Metabase expressions
    baseExpression = parseExpression(column.expr, context);
  } else if (column.field) {
    // Handle raw fields
    baseExpression = processFieldReference(column.field, column.table, tables);

    // Apply date binning if specified
    if (column.dateBin) {
      baseExpression = generateDateBinExpression(baseExpression, column.dateBin);
    }

    // Apply numeric binning if specified
    if (column.binCount) {
      baseExpression = generateNumericBinExpression(baseExpression, column.binCount, column.field);
    }
  } else {
    throw new Error('Column must have either field or expr');
  }

  // Apply aggregation if specified
  if (column.agg) {
    if (isWindowFunction(column.agg)) {
      baseExpression = generateWindowFunctionSQL(
        column.agg,
        baseExpression,
        column.partitionBy,
        column.orderBy,
        column.frame,
        context
      );
    } else if (isRegularAggregation(column.agg)) {
      baseExpression = generateAggregationSQL(column.agg, baseExpression);
    }
  }

  return baseExpression;
}

/**
 * Process a field reference and qualify it with table if needed
 */
function processFieldReference(
  field: string,
  explicitTable: string | undefined,
  tables: ProcessedTable[]
): string {
  let qualifiedField: string;

  if (explicitTable) {
    // Use explicitly specified table
    const table = tables.find(t => t.qualifiedName === explicitTable || t.name === explicitTable);
    if (!table) {
      throw new Error(`Table '${explicitTable}' not found`);
    }
    qualifiedField = qualifyFieldName(field, table);
  } else if (field.includes('.')) {
    // Field is already qualified
    const tableName = extractTableFromField(field);
    const table = findTableForField(field, tables);
    if (!table) {
      throw new Error(`Table '${tableName}' not found`);
    }
    qualifiedField = field;
  } else {
    // Unqualified field - use as-is (could be enhanced to auto-detect table)
    qualifiedField = field;
  }

  // Quote the field name for SQL
  return knex.ref(qualifiedField).toSQL().sql;
}

/**
 * Determine which columns need to be in GROUP BY
 */
export function determineGroupByColumns(processedColumns: ProcessedColumn[], hasJoins: boolean = false): string[] {
  return processedColumns
    .filter(col => {
      // Include only non-aggregated columns that are not expressions
      // Raw fields and fields with date/numeric binning should be grouped
      return !col.isAggregated && col.field && !col.expr;
    })
    .map(col => {
      // For joins, use the actual column reference instead of alias to avoid ambiguity
      if (hasJoins && col.field) {
        // Check if field is already table-qualified (e.g., "progress_history.Date")
        if (col.field.includes('.')) {
          const parts = col.field.split('.');
          if (parts.length === 2) {
            // Remove any existing quotes to avoid double quoting
            const tableName = parts[0].replace(/"/g, '');
            const fieldName = parts[1].replace(/"/g, '');
            return `"${tableName}"."${fieldName}"`;
          }
        } else if (col.table) {
          // Use table-qualified column name (avoid double quoting)
          const tableName = col.table.replace(/"/g, '');
          const fieldName = col.field.replace(/"/g, '');
          return `"${tableName}"."${fieldName}"`;
        } else if (col.sqlExpression && col.sqlExpression.includes('"."')) {
          // Extract the table-qualified column reference from the SQL expression
          // e.g., '"progress_history"."Date" AS "date"' -> '"progress_history"."Date"'
          const match = col.sqlExpression.match(/^(.+?)\s+AS\s+/);
          if (match) {
            return match[1];
          }
        }
      }

      if (col.dateBin && col.field) {
        // For date binning, use the actual date function expression
        let fieldRef: string;
        if (hasJoins && col.field.includes('.')) {
          const parts = col.field.split('.');
          const tableName = parts[0].replace(/"/g, '');
          const fieldName = parts[1].replace(/"/g, '');
          fieldRef = `"${tableName}"."${fieldName}"`;
        } else if (col.table) {
          const tableName = col.table.replace(/"/g, '');
          const fieldName = col.field.replace(/"/g, '');
          fieldRef = `"${tableName}"."${fieldName}"`;
        } else {
          const fieldName = col.field.replace(/"/g, '');
          fieldRef = `"${fieldName}"`;
        }
        return generateGroupByDateBinExpression(fieldRef, col.dateBin);
      }

      // Default to alias for non-join scenarios
      return col.alias;
    });
}

/**
 * Generate date binning expression for GROUP BY (simplified version)
 */
function generateGroupByDateBinExpression(fieldExpression: string, dateBin: string): string {
  switch (dateBin) {
    case 'day':
      return `DATE(${fieldExpression})`;
    case 'week':
      return `DATE_TRUNC('week', ${fieldExpression})`;
    case 'month':
      return `DATE_TRUNC('month', ${fieldExpression})`;
    case 'quarter':
      return `DATE_TRUNC('quarter', ${fieldExpression})`;
    case 'year':
      return `DATE_TRUNC('year', ${fieldExpression})`;
    case 'day_of_week':
      return `EXTRACT(DOW FROM ${fieldExpression})`;
    case 'month_of_year':
      return `EXTRACT(MONTH FROM ${fieldExpression})`;
    case 'quarter_of_year':
      return `EXTRACT(QUARTER FROM ${fieldExpression})`;
    default:
      return fieldExpression;
  }
}

/**
 * Check if any columns use aggregation
 */
export function hasAggregatedColumns(processedColumns: ProcessedColumn[]): boolean {
  return processedColumns.some(col => col.isAggregated);
}

/**
 * Check if any columns use window functions
 */
export function hasWindowFunctions(processedColumns: ProcessedColumn[]): boolean {
  return processedColumns.some(col => col.isWindowFunction);
}

/**
 * Generate SELECT clause SQL
 */
export function generateSelectSQL(processedColumns: ProcessedColumn[]): string {
  return processedColumns
    .map(col => `${col.sqlExpression} AS "${col.alias}"`)
    .join(', ');
}
