// Date binning utilities

import knexFactory from 'knex';
import * as process from 'process';
import { DateBin } from '../types';

const knex = knexFactory({ client: 'pg', connection: process.env.PG_URL });

/**
 * Generate SQL expression for date binning
 */
export function generateDateBinExpression(field: string, dateBin: DateBin): string {
  // field is already quoted by this point
  
  switch (dateBin) {
    case 'day':
      return `DATE(${field})`;
    
    case 'week':
      return `DATE_TRUNC('week', ${field})`;
    
    case 'month':
      return `DATE_TRUNC('month', ${field})`;
    
    case 'quarter':
      return `DATE_TRUNC('quarter', ${field})`;
    
    case 'year':
      return `DATE_TRUNC('year', ${field})`;
    
    case 'hour':
      return `DATE_TRUNC('hour', ${field})`;
    
    case 'minute':
      return `DATE_TRUNC('minute', ${field})`;
    
    case 'day_of_week':
      return `EXTRACT(DOW FROM ${field})`;
    
    case 'month_of_year':
      return `EXTRACT(MONTH FROM ${field})`;
    
    case 'quarter_of_year':
      return `EXTRACT(QUARTER FROM ${field})`;
    
    default:
      throw new Error(`Unsupported date bin: ${dateBin}`);
  }
}
