// New unified query builder implementation

import knexFactory from 'knex';
import * as process from 'process';
import { QueryConfig, ProcessedColumn, ProcessedTable, ProcessedJoin } from './types';
import { applyOrderByDefaults, autoGenerateGroupBy } from './utils/defaults';
import { processTable, processJoins, generateTableSQL, getAllowedColumns } from './utils/tableUtils';
import { processColumns, generateSelectSQL, determineGroupByColumns, hasAggregatedColumns, hasWindowFunctions } from './columns/processor';
import { applyFilters } from './filters/processor';

const knex = knexFactory({ client: 'pg', connection: process.env.PG_URL });

/**
 * Build a SQL query from the unified configuration
 */
export function buildQuery(config: QueryConfig) {
    // Validate basic configuration
    if (!config.table) {
        throw new Error('Table is required');
    }
    if (!config.columns || config.columns.length === 0) {
        throw new Error('At least one column is required');
    }

    // Process tables and joins
    const mainTable = processTable(config.table);
    const joins = processJoins(config.joins || [], mainTable, []);
    const allowedColumns = getAllowedColumns(mainTable, joins);

    // Process columns
    const processedColumns = processColumns(config.columns, mainTable, joins, allowedColumns);

    // Determine if we need GROUP BY
    const needsGroupBy = hasAggregatedColumns(processedColumns);
    const groupByColumns = needsGroupBy
        ? (config.groupBy || determineGroupByColumns(processedColumns))
        : [];

    // Start building the query
    let query = knex.select(knex.raw(generateSelectSQL(processedColumns)));

    // Add FROM clause with joins
    const tableSQL = generateTableSQL(mainTable, joins);
    query = query.from(knex.raw(tableSQL));

    // Apply filters
    applyFilters(
        query,
        config.filters || [],
        mainTable,
        joins,
        allowedColumns,
        needsGroupBy
    );

    // Add GROUP BY if needed
    if (needsGroupBy && groupByColumns.length > 0) {
        query = query.groupBy(groupByColumns.map(col => knex.ref(col)));
    }

    // Add ORDER BY
    if (config.orderBy && config.orderBy.length > 0) {
        config.orderBy.forEach(orderBy => {
            const processedOrderBy = applyOrderByDefaults(orderBy);
            query = query.orderBy(processedOrderBy.column, processedOrderBy.direction);
        });
    }

    // Add LIMIT and OFFSET
    if (config.limit) {
        query = query.limit(config.limit);
    }
    if (config.offset) {
        query = query.offset(config.offset);
    }

    return query;
}

/**
 * Legacy function for backward compatibility with old ChartConfig format
 */
export function buildQueryLegacy(config: any) {
    // Convert old format to new format
    const newConfig: QueryConfig = {
        table: config.table,
        columns: [],
        filters: config.filters,
        groupBy: config.groupBy,
        orderBy: config.orderBy,
        limit: config.limit,
        offset: config.offset
    };

    // Convert dimensions to columns
    if (config.dimensions) {
        config.dimensions.forEach((dim: any) => {
            if (typeof dim === 'string') {
                newConfig.columns.push({ field: dim });
            } else {
                newConfig.columns.push({
                    field: dim.field,
                    dateBin: dim.dateBin,
                    binCount: dim.binCount,
                    alias: dim.alias
                });
            }
        });
    }

    // Convert measures to columns
    if (config.measures) {
        config.measures.forEach((measure: any) => {
            if (measure.fn) {
                // Window function
                newConfig.columns.push({
                    field: measure.field,
                    agg: measure.fn,
                    partitionBy: measure.partitionBy,
                    orderBy: measure.orderBy,
                    frame: measure.frame,
                    alias: measure.alias
                });
            } else {
                // Regular aggregation
                newConfig.columns.push({
                    field: measure.field,
                    agg: measure.agg,
                    alias: measure.alias
                });
            }
        });
    }

    // Convert custom columns to columns
    if (config.customColumns) {
        config.customColumns.forEach((col: any) => {
            newConfig.columns.push({
                expr: col.expr,
                alias: col.alias
            });
        });
    }

    return buildQuery(newConfig);
}

/**
 * Cleanup function for database connections
 */
export async function cleanup() {
    await knex.destroy();
}

// Export types for external use
export * from './types';

// Legacy types for backward compatibility
export interface ChartConfig {
    table: string;
    dimensions?: (string | { field: string; dateBin?: string; binCount?: number; alias?: string })[];
    measures?: { field?: string; agg?: string; fn?: string; alias?: string; partitionBy?: string[]; orderBy?: string; frame?: any }[];
    customColumns?: { expr: string; alias: string }[];
    filters?: { field?: string; op?: string; value?: any; having?: boolean; expr?: string }[];
    groupBy?: string[];
    orderBy?: { column: string; direction?: 'asc' | 'desc' }[];
    limit?: number;
    offset?: number;
}

// Main export that handles both new and legacy formats
export function buildQueryAuto(config: QueryConfig | ChartConfig): any {
    // Check if it's the new format (has 'columns' property)
    if ('columns' in config) {
        return buildQuery(config as QueryConfig);
    } else {
        // Legacy format
        return buildQueryLegacy(config);
    }
}
