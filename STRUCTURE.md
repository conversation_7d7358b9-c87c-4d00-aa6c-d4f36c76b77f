# Project Structure

The query builder project has been organized into a clean, professional directory structure:

## Directory Layout

```
query_builder/
├── 📁 src/                          # Core source code
│   ├── 📄 index.ts                  # Main package entry point
│   ├── 📄 queryBuilder.ts           # Core query builder implementation
│   └── 📄 queryBuilder.js           # Compiled JavaScript (legacy)
│
├── 📁 examples/                     # Usage examples and demos
│   ├── 📄 main.ts                   # Basic usage example
│   ├── 📄 date_binning_examples.ts  # Comprehensive date binning examples
│   ├── 📄 config.json               # Sample configuration file
│   └── 📄 *.js                      # Compiled JavaScript files
│
├── 📁 tests/                        # Test suite
│   ├── 📄 test_date_binning.ts      # Date binning functionality tests
│   ├── 📄 test_all_window_functions.ts # Window functions tests
│   ├── 📄 test_date_binning_live.ts # Live database tests
│   ├── 📄 test_more_date_bins.ts    # Additional date binning tests
│   ├── 📄 test_no_groupby.ts        # Non-GROUP BY scenario tests
│   └── 📄 *.js                      # Compiled JavaScript files
│
├── 📁 docs/                         # Documentation
│   └── 📄 DATE_BINNING_REFERENCE.md # Complete date binning guide
│
├── 📁 dist/                         # Compiled TypeScript output (generated)
│   ├── 📁 src/                      # Compiled source code
│   ├── 📁 examples/                 # Compiled examples
│   └── 📁 tests/                    # Compiled tests
│
├── 📁 node_modules/                 # Dependencies (generated)
│
├── 📄 package.json                  # Package configuration
├── 📄 tsconfig.json                 # TypeScript configuration
├── 📄 README.md                     # Main documentation
├── 📄 STRUCTURE.md                  # This file
├── 📄 .gitignore                    # Git ignore rules
└── 📄 package-lock.json             # Dependency lock file
```

## Key Files

### Core Implementation
- **`src/queryBuilder.ts`** - Main query builder with date binning and window functions
- **`src/index.ts`** - Package entry point with clean exports

### Examples
- **`examples/main.ts`** - Basic usage with date binning
- **`examples/date_binning_examples.ts`** - All date binning options demonstrated

### Tests
- **`tests/test_date_binning.ts`** - Core date binning functionality
- **`tests/test_all_window_functions.ts`** - Window functions with GROUP BY support
- **`tests/test_date_binning_live.ts`** - Live database integration tests

### Documentation
- **`README.md`** - Main project documentation
- **`docs/DATE_BINNING_REFERENCE.md`** - Complete date binning reference

## NPM Scripts

```bash
# Build the project
npm run build

# Run examples
npm run example                    # Basic example
npm run example:date-binning      # Date binning examples

# Run tests
npm test                          # All window functions test
npm run test:date-binning        # Date binning tests
npm run test:live                # Live database tests

# Development
npm run build:watch              # Watch mode compilation
npm run clean                    # Clean build output
```

## Import Paths

With the new structure, imports are clean and organized:

```typescript
// Main package import
import { buildQuery, ChartConfig } from '../src/queryBuilder';

// Or using the index file
import { buildQuery, ChartConfig, DateBin } from '../src';
```

## Benefits of New Structure

1. **🎯 Clear Separation** - Source, examples, tests, and docs are clearly separated
2. **📦 Professional Layout** - Follows Node.js/TypeScript best practices
3. **🔧 Build System** - Proper TypeScript compilation with source maps
4. **📚 Documentation** - Comprehensive docs and examples
5. **🧪 Testing** - Organized test suite with different scenarios
6. **🚀 Easy Usage** - Simple npm scripts for common tasks
7. **📝 Type Safety** - Full TypeScript support with declaration files

## Migration Notes

- All import paths have been updated to use relative paths
- TypeScript compilation outputs to `dist/` directory
- Examples and tests can be run via npm scripts
- Legacy JavaScript files are preserved for compatibility
