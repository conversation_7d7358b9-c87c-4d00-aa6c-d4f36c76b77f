"use strict";
// Date binning utilities
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDateBinExpression = generateDateBinExpression;
const knex_1 = __importDefault(require("knex"));
const process = __importStar(require("process"));
const knex = (0, knex_1.default)({ client: 'pg', connection: process.env.PG_URL });
/**
 * Generate SQL expression for date binning
 */
function generateDateBinExpression(field, dateBin) {
    // field is already quoted by this point
    switch (dateBin) {
        case 'day':
            return `DATE(${field})`;
        case 'week':
            return `DATE_TRUNC('week', ${field})`;
        case 'month':
            return `DATE_TRUNC('month', ${field})`;
        case 'quarter':
            return `DATE_TRUNC('quarter', ${field})`;
        case 'year':
            return `DATE_TRUNC('year', ${field})`;
        case 'hour':
            return `DATE_TRUNC('hour', ${field})`;
        case 'minute':
            return `DATE_TRUNC('minute', ${field})`;
        case 'day_of_week':
            return `EXTRACT(DOW FROM ${field})`;
        case 'month_of_year':
            return `EXTRACT(MONTH FROM ${field})`;
        case 'quarter_of_year':
            return `EXTRACT(QUARTER FROM ${field})`;
        default:
            throw new Error(`Unsupported date bin: ${dateBin}`);
    }
}
