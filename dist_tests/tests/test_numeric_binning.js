"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test numeric binning functionality
const queryBuilder_1 = require("../src/queryBuilder");
async function runNumericBinningTest() {
    console.log('=== Testing Numeric Binning ===\n');
    // Test 1: Basic numeric binning with different bin counts
    console.log('1. Basic Numeric Binning Options:');
    const binCounts = [10, 20, 50, 100];
    for (const binCount of binCounts) {
        console.log(`\n--- ${binCount} bins for "Work done" ---`);
        let cfg = {
            "table": "progress_history",
            "columns": [
                { "field": "Scope", "binCount": binCount },
                { "field": "Date", "agg": "count", "alias": "record_count" },
                { "field": "Work done", "agg": "sum", "alias": "total_work" }
            ],
            "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
            "orderBy": [{ "column": `scope_bin_${binCount}`, "direction": "asc" }],
            "limit": 10
        };
        const q = (0, queryBuilder_1.buildQuery)(cfg);
        console.log('SQL:', q.toSQL().sql);
        // Execute the query
        try {
            const rows = await q;
            console.log(`✅ ${binCount} bins query executed successfully - ${rows.length} rows`);
            if (rows.length > 0) {
                console.table(rows.slice(0, 3));
            }
        }
        catch (error) {
            console.error(`❌ ${binCount} bins query failed:`, error instanceof Error ? error.message : String(error));
        }
    }
    // Test 2: Multiple numeric columns with binning
    console.log('\n\n2. Multiple Numeric Columns with Binning:');
    let cfg2 = {
        "table": "progress_history",
        "columns": [
            { "field": "Scope", "binCount": 20 },
            { "field": "Date", "agg": "count", "alias": "record_count" }
        ],
        "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
        "limit": 5
    };
    const q2 = (0, queryBuilder_1.buildQuery)(cfg2);
    console.log('SQL:', q2.toSQL().sql);
    // Execute the query
    try {
        const rows2 = await q2;
        console.log('✅ Multiple columns query executed successfully');
        console.log(`📊 Returned ${rows2.length} rows`);
        if (rows2.length > 0) {
            console.table(rows2.slice(0, 3));
        }
    }
    catch (error) {
        console.error('❌ Multiple columns query failed:', error instanceof Error ? error.message : String(error));
    }
    console.log();
    // Test 3: Mix of date binning and numeric binning
    console.log('\n\n3. Mixed Date and Numeric Binning:');
    let cfg3 = {
        "table": "progress_history",
        "columns": [
            { "field": "Date", "dateBin": "month" },
            { "field": "Scope", "binCount": 10 },
            { "field": "Sublayer" },
            { "field": "Work done", "agg": "sum", "alias": "total_work" }
        ],
        "filters": [{ "field": "Subactivity", "op": "eq", "value": "Piles Installed" }],
        "limit": 5
    };
    const q3 = (0, queryBuilder_1.buildQuery)(cfg3);
    console.log('SQL:', q3.toSQL().sql);
    // Execute the query
    try {
        const rows3 = await q3;
        console.log('✅ Mixed binning query executed successfully');
        console.log(`📊 Returned ${rows3.length} rows`);
        if (rows3.length > 0) {
            console.table(rows3.slice(0, 3));
        }
    }
    catch (error) {
        console.error('❌ Mixed binning query failed:', error instanceof Error ? error.message : String(error));
    }
    console.log();
    // Test 4: Custom alias for numeric binning
    console.log('\n\n4. Custom Alias for Numeric Binning:');
    let cfg4 = {
        "table": "progress_history",
        "columns": [
            { "field": "Scope", "binCount": 50, "alias": "scope_buckets" },
            { "field": "Date", "agg": "count", "alias": "bucket_count" }
        ],
        "limit": 5
    };
    const q4 = (0, queryBuilder_1.buildQuery)(cfg4);
    console.log('SQL:', q4.toSQL().sql);
    // Execute the query
    try {
        const rows4 = await q4;
        console.log('✅ Custom alias query executed successfully');
        console.log(`📊 Returned ${rows4.length} rows`);
        if (rows4.length > 0) {
            console.table(rows4.slice(0, 3));
        }
    }
    catch (error) {
        console.error('❌ Custom alias query failed:', error instanceof Error ? error.message : String(error));
    }
    console.log('\n=== Numeric Binning Test Complete ===');
    // Clean up and exit
    await (0, queryBuilder_1.cleanup)();
}
// Run the test
runNumericBinningTest().then(() => process.exit(0)).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
});
