# Query Builder Architecture

## Overview

The Query Builder is a sophisticated TypeScript-based SQL query generation system designed to handle complex analytical queries with advanced features like window functions, date binning, joins, and automatic GROUP BY detection. It provides a unified, type-safe interface for building complex SQL queries while abstracting away the intricacies of SQL generation.

## Core Design Principles

### 1. **Unified Column Structure**
- **Single `columns` array** instead of separate `dimensions` and `measures`
- **Flexible column definitions** supporting raw fields, aggregations, expressions, and window functions
- **Automatic type detection** and processing based on column properties

### 2. **Type Safety First**
- **Strong TypeScript typing** throughout the entire system
- **Compile-time validation** of query configurations
- **Runtime type checking** for critical operations

### 3. **Modular Architecture**
- **Separation of concerns** with dedicated modules for different functionalities
- **Composable components** that can be used independently
- **Clear interfaces** between modules

### 4. **Smart Automation**
- **Automatic GROUP BY detection** based on aggregation presence
- **Intelligent alias generation** with conflict resolution
- **Context-aware processing** for complex scenarios

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Query Builder Core                       │
├─────────────────────────────────────────────────────────────┤
│  buildQuery(config) → Knex Query Object                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Processing Pipeline                        │
├─────────────────────────────────────────────────────────────┤
│  1. Table & Join Processing                                 │
│  2. Column Processing (2-pass)                              │
│  3. GROUP BY Determination                                  │
│  4. SQL Generation                                          │
│  5. Query Assembly                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────┬─────────────────┬─────────────────┬─────────┐
│   Table Utils   │  Column Proc.   │  Window Funcs   │ Filters │
├─────────────────┼─────────────────┼─────────────────┼─────────┤
│ • JOIN logic    │ • Field proc.   │ • Nested agg.   │ • WHERE │
│ • Table refs    │ • Aggregations  │ • Partitioning  │ • HAVING│
│ • Aliases       │ • Date binning  │ • Ordering      │ • Types │
└─────────────────┴─────────────────┴─────────────────┴─────────┘
```

## Module Breakdown

### 1. **Core Query Builder** (`src/queryBuilder.ts`)

**Responsibilities:**
- Main entry point for query building
- Orchestrates the entire processing pipeline
- Manages Knex query object assembly

**Key Functions:**
- `buildQuery(config)`: Main function that processes configuration and returns Knex query
- Two-pass column processing for window function context
- Smart GROUP BY and ORDER BY handling

### 2. **Column Processing** (`src/columns/processor.ts`)

**Responsibilities:**
- Processes all column definitions
- Generates SQL expressions for each column
- Determines aggregation requirements
- Handles GROUP BY column determination

**Key Features:**
- **Two-pass processing**: First pass determines GROUP BY needs, second pass processes with context
- **Smart aggregation detection**: Detects aggregations in expressions using regex patterns
- **Context-aware processing**: Passes GROUP BY information to window functions

### 3. **Window Functions** (`src/columns/windowFunctions.ts`)

**Responsibilities:**
- Generates SQL for all window function types
- Handles nested aggregation for GROUP BY scenarios
- Resolves column references and aliases

**Advanced Features:**
- **Nested aggregation**: `SUM(SUM("field")) OVER (...)` for GROUP BY contexts
- **Smart column resolution**: Resolves aliases back to actual SQL expressions
- **Frame specification**: Supports custom window frames
- **All window function types**: cumsum, moving averages, ranking, analytical functions

### 4. **Table Utilities** (`src/utils/tableUtils.ts`)

**Responsibilities:**
- Processes table references and aliases
- Handles JOIN condition generation
- Manages table-qualified column names

**Key Features:**
- **Proper SQL quoting**: Ensures all identifiers are correctly quoted
- **JOIN condition generation**: Creates properly quoted JOIN conditions
- **Table alias support**: Handles complex multi-table scenarios

## Processing Flow

### Phase 1: Configuration Parsing
```typescript
interface ChartConfig {
  table: string | TableRef;
  columns: Column[];
  joins?: Join[];
  filters?: Filter[];
  orderBy?: OrderBy[];
  groupBy?: string[];
  having?: Filter[];
  limit?: number;
}
```

### Phase 2: Table & Join Processing
1. **Main table processing**: Convert string/object to `ProcessedTable`
2. **JOIN processing**: Generate SQL conditions with proper quoting
3. **Column validation**: Ensure all referenced tables exist

### Phase 3: Two-Pass Column Processing

#### **First Pass: GROUP BY Detection**
```typescript
const tempProcessedColumns = processColumns(config.columns, mainTable, joins, allowedColumns, false);
const needsGroupBy = hasAggregatedColumns(tempProcessedColumns);
```

#### **Second Pass: Context-Aware Processing**
```typescript
const processedColumns = processColumns(config.columns, mainTable, joins, allowedColumns, needsGroupBy);
```

**Why Two Passes?**
- Window functions need to know if there's a GROUP BY to use nested aggregation
- Alias resolution requires full column context
- Prevents circular dependencies in processing

### Phase 4: SQL Generation

#### **SELECT Clause Generation**
```typescript
function generateSelectSQL(processedColumns: ProcessedColumn[]): string {
  return processedColumns
    .map(col => `${col.sqlExpression} AS "${col.alias}"`)
    .join(', ');
}
```

#### **GROUP BY Logic**
```typescript
function determineGroupByColumns(processedColumns: ProcessedColumn[], hasJoins: boolean): string[] {
  return processedColumns
    .filter(col => !col.isAggregated && col.field && !col.expr)
    .map(col => {
      if (hasJoins && col.field.includes('.')) {
        // Use table-qualified names for joins
        return `"${tableName}"."${fieldName}"`;
      }
      return col.alias;
    });
}
```

## Advanced Features

### 1. **Window Functions with Nested Aggregation**

**Problem**: When there's a GROUP BY clause, window functions can't reference raw columns.

**Solution**: Automatic nested aggregation detection
```sql
-- Without GROUP BY
SUM("field") OVER (PARTITION BY "category" ORDER BY "date")

-- With GROUP BY (nested aggregation)
SUM(SUM("field")) OVER (PARTITION BY "category" ORDER BY "date")
```

### 2. **Smart Alias Resolution**

**Problem**: Window functions reference aliases, but need actual SQL expressions.

**Solution**: Context-aware column reference resolution
```typescript
function resolveColumnReference(col: string, hasGroupBy?: boolean, context?: ExpressionContext): string {
  if (context?.processedColumns) {
    const matchingColumn = context.processedColumns.find(pc => pc.alias === col);
    if (matchingColumn?.dateBin) {
      return generateDateBinExpression(matchingColumn.field, matchingColumn.dateBin);
    }
  }
  return `"${col}"`;
}
```

### 3. **Date Binning Integration**

**Features**:
- Multiple date binning types: day, week, month, quarter, year
- Automatic alias generation: `date_month`, `date_quarter`
- Integration with window functions and GROUP BY

**Implementation**:
```typescript
function generateDateBinExpression(field: string, dateBin: DateBin): string {
  switch (dateBin) {
    case 'month': return `DATE_TRUNC('month', ${field})`;
    case 'quarter': return `DATE_TRUNC('quarter', ${field})`;
    // ... other cases
  }
}
```

### 4. **JOIN Handling**

**Challenges**:
- Proper column qualification
- GROUP BY with table-qualified names
- Window function column references across tables

**Solutions**:
- Smart quoting in JOIN conditions
- Table-qualified GROUP BY columns
- Context-aware column resolution

## Error Handling & Validation

### 1. **Configuration Validation**
- Required field validation
- Type checking for all parameters
- Table reference validation

### 2. **SQL Generation Validation**
- Proper identifier quoting
- GROUP BY requirement checking
- Column reference validation

### 3. **Runtime Error Handling**
- Clear error messages for configuration issues
- SQL syntax error prevention
- Graceful degradation for edge cases

## Performance Considerations

### 1. **Efficient Processing**
- Two-pass processing minimizes redundant work
- Context sharing between modules
- Lazy evaluation where possible

### 2. **SQL Optimization**
- Proper indexing hints through column ordering
- Efficient JOIN condition generation
- Window function optimization

### 3. **Memory Management**
- Minimal object creation during processing
- Efficient string concatenation
- Context cleanup after processing

## Extension Points

### 1. **Custom Aggregations**
- Easy addition of new aggregation types
- Plugin architecture for custom functions

### 2. **New Window Functions**
- Extensible window function registry
- Custom frame specifications

### 3. **Additional Data Sources**
- Database-agnostic design
- Easy adapter pattern for new databases

## Testing Strategy

### 1. **Unit Tests**
- Individual module testing
- Edge case coverage
- Type safety validation

### 2. **Integration Tests**
- End-to-end query building
- Real database execution
- Complex scenario testing

### 3. **Performance Tests**
- Query generation speed
- Memory usage optimization
- Large dataset handling

## Detailed Processing Flows

### 1. **Column Processing Flow**

```mermaid
graph TD
    A[Column Definition] --> B{Has 'agg' property?}
    B -->|Yes| C{Is Window Function?}
    B -->|No| D{Has 'expr' property?}

    C -->|Yes| E[Window Function Processing]
    C -->|No| F[Regular Aggregation]

    D -->|Yes| G[Expression Processing]
    D -->|No| H[Raw Field Processing]

    E --> I[Check GROUP BY Context]
    I -->|Has GROUP BY| J[Use Nested Aggregation]
    I -->|No GROUP BY| K[Use Simple Aggregation]

    F --> L[Generate Aggregation SQL]
    G --> M[Parse Expression for Aggregations]
    H --> N[Generate Field Reference]

    J --> O[Generate Window Function SQL]
    K --> O
    L --> P[Add to Processed Columns]
    M --> P
    N --> P
    O --> P
```

### 2. **GROUP BY Determination Flow**

```mermaid
graph TD
    A[All Processed Columns] --> B{Any Aggregated Columns?}
    B -->|No| C[No GROUP BY Needed]
    B -->|Yes| D[GROUP BY Required]

    D --> E[Filter Non-Aggregated Columns]
    E --> F{Has JOINs?}

    F -->|Yes| G[Use Table-Qualified Names]
    F -->|No| H[Use Column Aliases]

    G --> I{Date Binning Column?}
    H --> I

    I -->|Yes| J[Use Date Function Expression]
    I -->|No| K[Use Column Reference]

    J --> L[Add to GROUP BY List]
    K --> L
```

### 3. **Window Function Resolution Flow**

```mermaid
graph TD
    A[Window Function Definition] --> B[Extract partitionBy/orderBy]
    B --> C{Has GROUP BY Context?}

    C -->|Yes| D[Use Nested Aggregation Pattern]
    C -->|No| E[Use Simple Aggregation Pattern]

    D --> F[Wrap with Aggregation Function]
    E --> G[Use Raw Field Reference]

    F --> H[Resolve Column References]
    G --> H

    H --> I{Reference is Alias?}
    I -->|Yes| J[Look up in Processed Columns]
    I -->|No| K[Use Direct Reference]

    J --> L{Is Date Binning Column?}
    K --> M[Quote Column Name]

    L -->|Yes| N[Generate Date Function]
    L -->|No| O[Use Field Reference]

    N --> P[Generate Window Clause]
    O --> P
    M --> P
```

## Key Architectural Decisions

### 1. **Why Two-Pass Column Processing?**

**Problem**: Window functions need to know about GROUP BY context, but GROUP BY determination needs processed columns.

**Decision**: Implement two-pass processing
- **Pass 1**: Quick processing to determine GROUP BY needs
- **Pass 2**: Full processing with GROUP BY context

**Benefits**:
- Resolves circular dependency
- Enables context-aware window function generation
- Maintains clean separation of concerns

### 2. **Why Unified Column Structure?**

**Problem**: Traditional BI tools separate dimensions and measures, causing complexity.

**Decision**: Single `columns` array with flexible column definitions

**Benefits**:
- Simpler API surface
- More flexible column definitions
- Easier to add new column types
- Reduces configuration complexity

### 3. **Why Context-Aware Processing?**

**Problem**: Different SQL contexts require different column reference strategies.

**Decision**: Pass context information through processing pipeline

**Benefits**:
- Enables smart column reference resolution
- Supports complex scenarios like JOINs with window functions
- Maintains SQL correctness across all scenarios

### 4. **Why Smart Quoting Strategy?**

**Problem**: Knex.js automatic quoting conflicts with pre-quoted identifiers.

**Decision**: Conditional quoting based on content analysis
```typescript
query.groupBy(groupByColumns.map(col => {
  // If column contains function calls or table-qualified names, use raw
  if (col.includes('"."') || col.includes('(')) {
    return knex.raw(col);
  }
  // Otherwise use ref for proper quoting
  return knex.ref(col);
}));
```

**Benefits**:
- Prevents double-quoting issues
- Maintains proper SQL identifier quoting
- Works with complex expressions and table-qualified names

## Data Flow Architecture

### 1. **Input Processing**
```
User Config → Validation → Normalization → Processing Pipeline
```

### 2. **Column Processing Pipeline**
```
Raw Column → Type Detection → SQL Generation → Context Application → Final SQL
```

### 3. **Query Assembly**
```
Processed Components → Knex Query Building → SQL Generation → Execution
```

## Error Handling Strategy

### 1. **Validation Errors**
- **Early detection**: Validate configuration before processing
- **Clear messages**: Provide actionable error messages
- **Type safety**: Leverage TypeScript for compile-time validation

### 2. **Processing Errors**
- **Graceful degradation**: Handle edge cases without crashing
- **Context preservation**: Maintain error context for debugging
- **Recovery strategies**: Provide fallback behaviors where possible

### 3. **SQL Generation Errors**
- **Syntax validation**: Ensure generated SQL is syntactically correct
- **Reference validation**: Verify all column references are valid
- **Compatibility checks**: Ensure SQL works with target database

## Implementation Examples

### 1. **Complex Query Configuration**

```typescript
const complexConfig: ChartConfig = {
  table: { name: "progress_history", alias: "ph" },
  joins: [
    {
      table: { name: "progress_data", alias: "pd" },
      type: "left",
      on: { left: "ph.Sublayer", right: "pd.Sublayer" }
    }
  ],
  columns: [
    // Date binning
    { field: "ph.Date", dateBin: "month", alias: "month" },

    // Raw field
    { field: "ph.Sublayer" },

    // Regular aggregation
    { field: "ph.Work done", agg: "sum", alias: "actual_work" },

    // Window function with partition
    {
      field: "pd.Scope",
      agg: "cumsum",
      partitionBy: ["ph.Sublayer"],
      orderBy: "month",
      alias: "running_planned"
    },

    // Custom expression
    { expr: "round(avg([ph.Work done]), 2)", alias: "avg_work" }
  ],
  filters: [
    { field: "ph.Subactivity", op: "eq", value: "Piles Installed" }
  ],
  orderBy: [
    { column: "month", direction: "asc" },
    { column: "sublayer", direction: "asc" }
  ],
  limit: 100
};
```

### 2. **Generated SQL Output**

```sql
SELECT
  DATE_TRUNC('month', "ph"."Date") AS "month",
  "ph"."Sublayer" AS "sublayer",
  SUM("ph"."Work done") AS "actual_work",
  SUM(SUM("pd"."Scope")) OVER (
    PARTITION BY "ph"."Sublayer"
    ORDER BY DATE_TRUNC('month', "ph"."Date")
    ROWS UNBOUNDED PRECEDING
  ) AS "running_planned",
  ROUND(AVG("ph"."Work done"), 2) AS "avg_work"
FROM "progress_history" AS "ph"
LEFT JOIN "progress_data" AS "pd" ON "ph"."Sublayer" = "pd"."Sublayer"
WHERE "ph"."Subactivity" = ?
GROUP BY DATE_TRUNC('month', "ph"."Date"), "ph"."Sublayer"
ORDER BY "month" ASC, "sublayer" ASC
LIMIT ?
```

### 3. **Key Implementation Patterns**

#### **Pattern 1: Context-Aware Processing**
```typescript
// Pass context through processing pipeline
const context: ExpressionContext = {
  allowedColumns,
  tables: allTables,
  hasGroupBy: needsGroupBy,
  processedColumns: processedColumns
};

const sqlExpression = generateColumnSQL(column, context, allTables);
```

#### **Pattern 2: Smart Aggregation Detection**
```typescript
function containsAggregation(expr: string): boolean {
  const aggregationFunctions = [
    'sum', 'avg', 'count', 'max', 'min', 'median'
  ];

  const lowerExpr = expr.toLowerCase();
  return aggregationFunctions.some(func => {
    const pattern = new RegExp(`\\b${func}\\s*\\(`, 'i');
    return pattern.test(lowerExpr);
  });
}
```

#### **Pattern 3: Conditional SQL Generation**
```typescript
function generateWindowFunctionSQL(
  agg: AggregationType,
  expression: string,
  partitionBy?: string[],
  orderBy?: string | string[],
  context?: ExpressionContext
): string {
  const hasGroupBy = context?.hasGroupBy || false;
  const needsNestedAgg = hasGroupBy && needsAggregationNesting(agg);
  const finalExpression = needsNestedAgg
    ? wrapWithAggregation(expression, agg)
    : expression;

  return `SUM(${finalExpression}) OVER (${windowClause})`;
}
```

## Performance Optimizations

### 1. **Lazy Evaluation**
- Column processing only when needed
- SQL generation deferred until query execution
- Context sharing to avoid recomputation

### 2. **Efficient String Building**
- Template literals for SQL generation
- Minimal string concatenation
- Reuse of common SQL fragments

### 3. **Memory Management**
- Object pooling for frequently created objects
- Cleanup of processing contexts
- Minimal intermediate object creation

## Future Enhancements

### 1. **Query Optimization**
- **Automatic index hints**: Suggest optimal indexes based on query patterns
- **Query plan analysis**: Integration with database query planners
- **Performance monitoring**: Built-in query performance tracking

### 2. **Advanced Analytics**
- **Statistical functions**: Built-in statistical analysis functions
- **Time series analysis**: Advanced time-based calculations
- **Machine learning integration**: Support for ML model predictions

### 3. **Multi-Database Support**
- **Database adapters**: Support for different SQL dialects
- **Cross-database queries**: Federated query capabilities
- **NoSQL integration**: Support for document and graph databases

### 4. **Developer Experience**
- **Query visualization**: Visual query builder interface
- **Performance profiling**: Built-in query performance analysis
- **Auto-completion**: Intelligent field and function suggestions

## Conclusion

This architecture represents a sophisticated approach to SQL query generation that balances:

- **Flexibility**: Supports complex analytical queries with minimal configuration
- **Type Safety**: Leverages TypeScript for compile-time validation
- **Performance**: Optimized processing pipeline with smart caching
- **Extensibility**: Modular design allows easy addition of new features
- **Maintainability**: Clear separation of concerns and well-defined interfaces

The two-pass processing approach, context-aware column resolution, and smart SQL generation make this query builder capable of handling enterprise-level analytical workloads while maintaining code clarity and developer productivity.
